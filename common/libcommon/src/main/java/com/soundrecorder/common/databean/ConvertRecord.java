/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ConvertRecord.java
 * * Description: ConvertRecord.java
 * * Version: 1.0
 * * Date : 2019/9/25
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * yixiaoming  2019/9/25      1.0    build this module
 ****************************************************************/

package com.soundrecorder.common.databean;

import android.content.ContentValues;

import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.constant.DatabaseConstant;
import com.soundrecorder.common.share.ShareUtil;

import java.util.ArrayList;
import java.util.List;

public class ConvertRecord {

    private static final String TAG = "ConvertRecord";

    private long mId;
    private long mRecordId;
    private String mMediaPath;
    private String mConvertTextfilePath;
    private String mChunkName;
    private int mCompleteStatus;
    private String mOnlyId;
    private int mVersion;
    private String mTaskId;
    private String mUploadRequestId;
    private String mUploadKey;
    private int mPartCount;
    private int mUploadStatus;
    private int mConvertStatus;
    private String mUploadAllUrl;
    private int mServerPlanCode;
    private String mHistoryRoleName;
    private int mCanShowSpeakerRole;
    private int mSpeakerRoleIsShowing;
    private int mSpeakerRoleOriginalNumber;
    private int mSpeakerRoleHasFirstshow;

    private List<UploadRecord> mUploadRecordList;

    /*是否开启定向录音*/
    private boolean mIsDirectOn;
    /*定向录音开始、结束时间:0-1,2-3,3-4*/
    private String mDirectTime;

    public ConvertRecord() {
    }

    public ConvertRecord(long recordId) {
        mRecordId = recordId;
    }

    public ConvertRecord(long id, long recordId, String mediaPath, String convertTextfilePath, String chunkName, int completeStatus) {
        mId = id;
        mRecordId = recordId;
        mMediaPath = mediaPath;
        mConvertTextfilePath = convertTextfilePath;
        mChunkName = chunkName;
        mCompleteStatus = completeStatus;
    }

    public ConvertRecord(long recordId, String mediaPath, String convertTextfilePath, String chunkName, int completeStatus,
                         String onlyId, int version, String taskId, String uploadRequestId, String uploadKey,
                         int partCount, int uploadStatus, int convertStatus,String historyRoleName,int serverPlanCode, int canShowSpeakerRole) {
        mRecordId = recordId;
        mMediaPath = mediaPath;
        mConvertTextfilePath = convertTextfilePath;
        mChunkName = chunkName;
        mCompleteStatus = completeStatus;
        mOnlyId = onlyId;
        mVersion = version;
        mTaskId = taskId;
        mUploadRequestId = uploadRequestId;
        mUploadKey = uploadKey;
        mPartCount = partCount;
        mUploadStatus = uploadStatus;
        mConvertStatus = convertStatus;
        mHistoryRoleName = historyRoleName;
        mServerPlanCode = serverPlanCode;
        mCanShowSpeakerRole = canShowSpeakerRole;
    }


    public void copy(ConvertRecord record) {
        mId = record.mId;
        mRecordId = record.mRecordId;
        mMediaPath = record.mMediaPath;
        mConvertTextfilePath = record.mConvertTextfilePath;
        mChunkName = record.mChunkName;
        mCompleteStatus = record.mCompleteStatus;
        mOnlyId = record.mOnlyId;
        mVersion = record.mVersion;
        mTaskId = record.mTaskId;
        mUploadRequestId = record.mUploadRequestId;
        mUploadKey = record.mUploadKey;
        mPartCount = record.mPartCount;
        mUploadStatus = record.mUploadStatus;
        mConvertStatus = record.mConvertStatus;
        mUploadAllUrl = record.mUploadAllUrl;
        mUploadRecordList = new ArrayList<>();
        for (int i = 0; i < record.mUploadRecordList.size(); i++) {
            mUploadRecordList.add(new UploadRecord(record.mUploadRecordList.get(i)));
        }
        mHistoryRoleName = record.mHistoryRoleName;
        mServerPlanCode = record.mServerPlanCode;
        mCanShowSpeakerRole = record.mCanShowSpeakerRole;
        mIsDirectOn = record.mIsDirectOn;
        mDirectTime = record.mDirectTime;
    }

    public void printConvertRecord() {
        DebugUtil.i(TAG, "==>ConvertRecord:\n" + toString());
        for (int i = 0; i < mUploadRecordList.size(); i++) {
            UploadRecord uploadRecord = mUploadRecordList.get(i);
            if (uploadRecord != null) {
                DebugUtil.i(TAG, "==>uploadRecord:\n" + uploadRecord.toString());
            }
        }
    }


    public long getId() {
        return mId;
    }

    public void setId(long id) {
        mId = id;
    }

    public long getRecordId() {
        return mRecordId;
    }

    public void setRecordId(long recordId) {
        mRecordId = recordId;
    }

    public String getMediaPath() {
        return mMediaPath;
    }

    public void setMediaPath(String mediaPath) {
        mMediaPath = mediaPath;
    }

    public String getConvertTextfilePath() {
        return mConvertTextfilePath;
    }

    public void setConvertTextfilePath(String convertTextfilePath) {
        mConvertTextfilePath = convertTextfilePath;
    }

    public String getChunkName() {
        return mChunkName;
    }

    public void setChunkName(String chunkName) {
        mChunkName = chunkName;
    }

    public int getCompleteStatus() {
        return mCompleteStatus;
    }

    public void setCompleteStatus(int completeStatus) {
        mCompleteStatus = completeStatus;
    }


    public String getOnlyId() {
        return mOnlyId;
    }

    public void setOnlyId(String mOnlyId) {
        this.mOnlyId = mOnlyId;
    }

    public int getVersion() {
        return mVersion;
    }

    public void setVersion(int mVersion) {
        this.mVersion = mVersion;
    }

    public String getTaskId() {
        return mTaskId;
    }

    public void setTaskId(String mTaskId) {
        this.mTaskId = mTaskId;
    }

    public String getUploadRequestId() {
        return mUploadRequestId;
    }

    public void setUploadRequestId(String mUploadTaskId) {
        this.mUploadRequestId = mUploadTaskId;
    }

    public int getPartCount() {
        return mPartCount;
    }

    public void setPartCount(int mPartCount) {
        this.mPartCount = mPartCount;
    }

    public String getUploadKey() {
        return mUploadKey;
    }

    public void setUploadKey(String mUploadKey) {
        this.mUploadKey = mUploadKey;
    }

    public List<UploadRecord> getUploadRecordList() {
        return mUploadRecordList;
    }

    public void setUploadRecordList(List<UploadRecord> mUploadRecordList) {
        this.mUploadRecordList = mUploadRecordList;
    }

    public int getUploadStatus() {
        return mUploadStatus;
    }

    public void setUploadStatus(int mUploadStatus) {
        this.mUploadStatus = mUploadStatus;
    }

    public int getConvertStatus() {
        return mConvertStatus;
    }

    public void setConvertStatus(int mConvertStatus) {
        this.mConvertStatus = mConvertStatus;
    }

    public String getUploadAllUrl() {
        return mUploadAllUrl;
    }

    public void setUploadAllUrl(String mUploadAllUrl) {
        this.mUploadAllUrl = mUploadAllUrl;
    }

    public String getHistoryRoleName() {
        return mHistoryRoleName;
    }

    public void setHistoryRoleName(String mHistoryRoleName) {
        this.mHistoryRoleName = mHistoryRoleName;
    }

    public int getSpeakerRoleIsShowing() {
        return mSpeakerRoleIsShowing;
    }

    public void setSpeakerRoleIsShowing(int canShowSpeakerRole) {
        this.mSpeakerRoleIsShowing = canShowSpeakerRole;
    }

    public int getSpeakerRoleOriginalNumber() {
        return mSpeakerRoleOriginalNumber;
    }

    public void setSpeakerRoleOriginalNumber(int speakerRoleOriginalNumber) {
        this.mSpeakerRoleOriginalNumber = speakerRoleOriginalNumber;
    }

    public int getSpeakerRoleHasFirstshow() {
        return mSpeakerRoleHasFirstshow;
    }

    public void setSpeakerRoleHasFirstshow(int speakerRoleHasFirstshow) {
        this.mSpeakerRoleHasFirstshow = speakerRoleHasFirstshow;
    }

    public int getCanShowSpeakerRole() {
        return mCanShowSpeakerRole;
    }

    public void setCanShowSpeakerRole(int canShowSpeakerRole) {
        this.mCanShowSpeakerRole = canShowSpeakerRole;
    }

    public int getServerPlanCode() {
        return mServerPlanCode;
    }

    public void setServerPlanCode(int mServerPlanCode) {
        this.mServerPlanCode = mServerPlanCode;
    }

    public boolean getIsDirectOn() {
        return mIsDirectOn;
    }

    public void setIsDirectOn(boolean mIsDirectOn) {
        this.mIsDirectOn = mIsDirectOn;
    }

    public String getDirectTime() {
        return mDirectTime;
    }

    public void setDirectTime(String mDirectTime) {
        this.mDirectTime = mDirectTime;
    }

    public ContentValues getContentValuesWithoutId() {
        ContentValues values = new ContentValues();
        values.put(DatabaseConstant.ConvertColumn.RECORD_ID, this.mRecordId);
        values.put(DatabaseConstant.ConvertColumn.MEDIA_PATH, this.mMediaPath);
        values.put(DatabaseConstant.ConvertColumn.CONVERT_TEXTFILE_PATH, this.mConvertTextfilePath);
        values.put(DatabaseConstant.ConvertColumn.CHUNK_NAME, this.mChunkName);
        values.put(DatabaseConstant.ConvertColumn.COMPLETE_STATUS, this.mCompleteStatus);
        values.put(DatabaseConstant.ConvertColumn.ONLY_ID, this.mOnlyId);
        values.put(DatabaseConstant.ConvertColumn.VERSION, this.mVersion);
        values.put(DatabaseConstant.ConvertColumn.TASKID, this.mTaskId);
        values.put(DatabaseConstant.ConvertColumn.UPLOAD_REQUEST_ID, this.mUploadRequestId);
        values.put(DatabaseConstant.ConvertColumn.UPLOAD_KEY, this.mUploadKey);
        values.put(DatabaseConstant.ConvertColumn.PART_COUNT, this.mPartCount);
        values.put(DatabaseConstant.ConvertColumn.UPLOAD_STATUS, this.mUploadStatus);
        values.put(DatabaseConstant.ConvertColumn.CONVERT_STATUS, this.mConvertStatus);
        values.put(DatabaseConstant.ConvertColumn.UPLOAD_ALL_URL, this.mUploadAllUrl);
        values.put(DatabaseConstant.ConvertColumn.HISTORY_ROLENAME, this.mHistoryRoleName);
        values.put(DatabaseConstant.ConvertColumn.SERVER_PLAN_CODE, this.mServerPlanCode);
        values.put(DatabaseConstant.ConvertColumn.CAN_SHOW_SPEAKER_ROLE, this.mCanShowSpeakerRole);
        values.put(DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ISSHOWING, this.mSpeakerRoleIsShowing);
        values.put(DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ORIGINAL_NUMBER, this.mSpeakerRoleOriginalNumber);
        values.put(DatabaseConstant.ConvertColumn.SPEAKER_ROLE_HAS_FIRSTSHOW, this.mSpeakerRoleHasFirstshow);

        values.put(DatabaseConstant.ConvertColumn.IS_DIRECT_ON, this.mIsDirectOn);
        values.put(DatabaseConstant.ConvertColumn.DIRECT_TIME, this.mDirectTime);
        return values;
    }

    public ContentValues getContentValues() {
        ContentValues values = new ContentValues();
        values.put(DatabaseConstant.ConvertColumn._ID, this.mId);
        values.put(DatabaseConstant.ConvertColumn.RECORD_ID, this.mRecordId);
        values.put(DatabaseConstant.ConvertColumn.MEDIA_PATH, this.mMediaPath);
        values.put(DatabaseConstant.ConvertColumn.CONVERT_TEXTFILE_PATH, this.mConvertTextfilePath);
        values.put(DatabaseConstant.ConvertColumn.CHUNK_NAME, this.mChunkName);
        values.put(DatabaseConstant.ConvertColumn.COMPLETE_STATUS, this.mCompleteStatus);
        values.put(DatabaseConstant.ConvertColumn.ONLY_ID, this.mOnlyId);
        values.put(DatabaseConstant.ConvertColumn.VERSION, this.mVersion);
        values.put(DatabaseConstant.ConvertColumn.TASKID, this.mTaskId);
        values.put(DatabaseConstant.ConvertColumn.UPLOAD_REQUEST_ID, this.mUploadRequestId);
        values.put(DatabaseConstant.ConvertColumn.UPLOAD_KEY, this.mUploadKey);
        values.put(DatabaseConstant.ConvertColumn.PART_COUNT, this.mPartCount);
        values.put(DatabaseConstant.ConvertColumn.UPLOAD_STATUS, this.mUploadStatus);
        values.put(DatabaseConstant.ConvertColumn.CONVERT_STATUS, this.mConvertStatus);
        values.put(DatabaseConstant.ConvertColumn.UPLOAD_ALL_URL, this.mUploadAllUrl);
        values.put(DatabaseConstant.ConvertColumn.HISTORY_ROLENAME, this.mHistoryRoleName);
        values.put(DatabaseConstant.ConvertColumn.SERVER_PLAN_CODE, this.mServerPlanCode);
        values.put(DatabaseConstant.ConvertColumn.CAN_SHOW_SPEAKER_ROLE, this.mCanShowSpeakerRole);
        values.put(DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ISSHOWING, this.mSpeakerRoleIsShowing);
        values.put(DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ORIGINAL_NUMBER, this.mSpeakerRoleOriginalNumber);
        values.put(DatabaseConstant.ConvertColumn.SPEAKER_ROLE_HAS_FIRSTSHOW, this.mSpeakerRoleHasFirstshow);

        values.put(DatabaseConstant.ConvertColumn.IS_DIRECT_ON, this.mIsDirectOn);
        values.put(DatabaseConstant.ConvertColumn.DIRECT_TIME, this.mDirectTime);

        return values;
    }

    public static String[] getProjections() {
        return new String[]{
                DatabaseConstant.ConvertColumn._ID,
                DatabaseConstant.ConvertColumn.RECORD_ID,
                DatabaseConstant.ConvertColumn.MEDIA_PATH,
                DatabaseConstant.ConvertColumn.CONVERT_TEXTFILE_PATH,
                DatabaseConstant.ConvertColumn.CHUNK_NAME,
                DatabaseConstant.ConvertColumn.COMPLETE_STATUS,
                DatabaseConstant.ConvertColumn.ONLY_ID,
                DatabaseConstant.ConvertColumn.VERSION,
                DatabaseConstant.ConvertColumn.TASKID,
                DatabaseConstant.ConvertColumn.UPLOAD_REQUEST_ID,
                DatabaseConstant.ConvertColumn.UPLOAD_KEY,
                DatabaseConstant.ConvertColumn.PART_COUNT,
                DatabaseConstant.ConvertColumn.UPLOAD_STATUS,
                DatabaseConstant.ConvertColumn.CONVERT_STATUS,
                DatabaseConstant.ConvertColumn.UPLOAD_ALL_URL,
                DatabaseConstant.ConvertColumn.HISTORY_ROLENAME,
                DatabaseConstant.ConvertColumn.SERVER_PLAN_CODE,
                DatabaseConstant.ConvertColumn.CAN_SHOW_SPEAKER_ROLE,
                DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ISSHOWING,
                DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ORIGINAL_NUMBER,
                DatabaseConstant.ConvertColumn.SPEAKER_ROLE_HAS_FIRSTSHOW,
                DatabaseConstant.ConvertColumn.IS_DIRECT_ON,
                DatabaseConstant.ConvertColumn.DIRECT_TIME,
        };
    }

    @Override
    public String toString() {
        return "ConvertRecord{"
                + "mId=" + mId
                + ", mRecordId=" + mRecordId
                + ", mMediaPath='" + mMediaPath + '\''
                + ", mConvertTextfilePath='" + mConvertTextfilePath + '\''
                + ", mChunkName='" + mChunkName + '\''
                + ", mCompleteStatus=" + mCompleteStatus
                + ", mOnlyId='" + mOnlyId + '\''
                + ", mVersion=" + mVersion
                + ", mTaskId='" + mTaskId + '\''
                + ", mUploadRequestId='" + mUploadRequestId + '\''
                + ", mUploadKey='" + mUploadKey + '\''
                + ", mPartCount=" + mPartCount
                + ", mUploadStatus=" + mUploadStatus
                + ", mConvertStatus=" + mConvertStatus
                + ", mUploadAllUrl= " + mUploadAllUrl
                + ", mUploadRecordList=" + mUploadRecordList
                + ", mHistoryRoleName =" + mHistoryRoleName
                + ", mServerPlanCode =" + mServerPlanCode
                + ", mCanShowSpeakerRole =" + mCanShowSpeakerRole
                + ", mSpeakerRoleIsShowing =" + mSpeakerRoleIsShowing
                + ", mSpeakerRoleOriginalNumber =" + mSpeakerRoleOriginalNumber
                + ", mSpeakerRoleHasFirstshow =" + mSpeakerRoleHasFirstshow
                + ", mIsDirectOn =" + mIsDirectOn
                + ", mDirectTime =" + mDirectTime
                + '}';
    }

    /**
     * 判断文件是否是在OShare目录下
     */
    public boolean isOShareFile() {
        return mConvertTextfilePath.contains(ShareUtil.INSTANCE.getRelativePathOShareText());
    }
}
