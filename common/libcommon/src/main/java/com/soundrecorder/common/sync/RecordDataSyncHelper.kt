package com.soundrecorder.common.sync

import android.content.Context
import android.os.SystemClock
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.base.utils.SyncTimeUtils
import com.soundrecorder.common.executor.ExecutorManager
import com.soundrecorder.common.sync.db.RecordDataSync
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState
import com.soundrecorder.modulerouter.cloudkit.SYNC_TYPE_DEFAULT
import com.soundrecorder.modulerouter.cloudkit.SYNC_TYPE_RECOVERY_START_APP
import com.soundrecorder.modulerouter.utils.Injector
import kotlin.math.abs

object RecordDataSyncHelper {
    const val TAG = "RecordDataSyncHelper"

    /*清除数据后第一次进入录音，是否拦截云同步操作*/
    var mIsInterceptFirstEnterSync = false

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    /**
     * 前提：非第一次打开应用
     * 距离上一次媒体库全量超过 1小时
     * 触发： 媒体库全量 + 云端增量
     * @return true: 非第一次打开应用 false：第一次打开应用(清除应用数据后打开)
     */
    @JvmStatic
    fun chekLastRecordSyncOverOneHour(): Boolean {
        val appContext = BaseApplication.getAppContext()
        val lastEnterTime = PrefUtil.getSharedPreferences(appContext)?.getLong(PrefUtil.KEY_LAST_ENTER_TIME, 0L)
                ?: return false
        val intervalTime = SystemClock.elapsedRealtime() - lastEnterTime
        DebugUtil.i(TAG, "checkNeedSyncByLastEnterTime intervalTime: $intervalTime")
        if (lastEnterTime != 0L) {
            // 非清空应用数据第一次进来,intervalTime再手机重启后进入，差值为负数，需要使用绝对值
            if (abs(intervalTime) > SyncTimeUtils.TIME_1_HOUR) {
                DebugUtil.i(TAG, "checkNeedSyncByLastEnterTime over 1 hour")
                trigRecordSync(appContext)
                updateMediaCompareTime(appContext)
                return true
            }
        } else {
            // 初次进入APP
            DebugUtil.i(TAG, "checkNeedSyncByLastEnterTime is zero")
            updateMediaCompareTime(appContext)
        }
        return false
    }

    @JvmStatic
    fun trigRecordSync(appContext: Context) {
        DebugUtil.i(TAG, "trigRecordSync, mIsInterceptFirstEnterSync $mIsInterceptFirstEnterSync")
        // 支持云同步 && 非用户第一次点击不同意进入 录音，执行媒体库全量+云同步（前提：开关打开)
        if (!mIsInterceptFirstEnterSync && cloudKitApi?.isSupportCloudArea() == true) {
            val executeResult = cloudKitApi?.scheduleSyncRunnable(appContext, {
                val switchStateOn = (cloudKitApi?.queryCloudSwitchState(false) ?: -1) > CloudSwitchState.CLOSE
                DebugUtil.i(TAG, "trigRecordSync,switchStateOn $switchStateOn ")
                if (switchStateOn) {
                    cloudKitApi?.trigMediaDBSync(appContext, SYNC_TYPE_RECOVERY_START_APP)
                } else {
                    // 云同步开关关闭：执行媒体库全量对比
                    doMediaFullCompare()
                }
            }, NumberConstant.NUM_500.toLong())
            DebugUtil.i(TAG, "trigRecordSync,execute runnable result $executeResult ")
            // 触发云同步操作失败，执行媒体库全量对比
            if (executeResult == false) {
                doMediaFullCompare()
            }
        } else {
            // 不支持云同步区域 or 用户点击了不同意(用户须知)，仅触发媒体库全量对比
            doMediaFullCompare()
        }
    }

    @JvmStatic
    fun doMediaFullCompare() {
        DebugUtil.i(TAG, "doMediaFullCompare, mIsInterceptFirstEnterSync $mIsInterceptFirstEnterSync")
        mIsInterceptFirstEnterSync = false
        ExecutorManager.recordDataSyncExecutor?.execute {
            RecordDataSync.getInstance().syncAllRecordDataFromMedia(BaseApplication.getAppContext(), true, false, 0)
        }
    }

    /**
     * 更新媒体库全量对比时间
     */
    @JvmStatic
    fun updateMediaCompareTime(appContext: Context) {
        PrefUtil.putLong(appContext, PrefUtil.KEY_LAST_ENTER_TIME, SystemClock.elapsedRealtime())
    }

    @JvmStatic
    fun scheduleSyncRunnable() {
        ExecutorManager.recordDataSyncExecutor?.execute {
            cloudKitApi?.doMediaCompare(false, SYNC_TYPE_DEFAULT)
        }
    }
}