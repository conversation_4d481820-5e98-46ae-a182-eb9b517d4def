/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: RecordBulkInsert
 ** Description:
 ** Version: 1.0
 ** Date : 2019-07-11
 ** Author: huangyuanwang
 **
 ** v1.0, 2019-3-12, mapengfei, create
 ****************************************************************/
package com.soundrecorder.common.sync.db;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.net.Uri;
import android.os.RemoteException;

import java.util.ArrayList;

public class RecordBulkInsert {
    private static final int CLAUSE_SIZE_MAX = 50;
    private ArrayList<ContentValues> mValues = new ArrayList<ContentValues>(CLAUSE_SIZE_MAX);
    private ContentResolver mProvider;
    private Uri mBaseUri;

    public RecordBulkInsert(ContentResolver provider, Uri baseUri) {
        mProvider = provider;
        mBaseUri = baseUri;
    }

    public void insert(ContentValues calues) throws RemoteException {
        mValues.add(calues);

        if (mValues.size() >= CLAUSE_SIZE_MAX) {
            flush();
        }
    }

    public void flush() throws RemoteException {
        int size = mValues.size();

        if (size > 0) {
            ContentValues[] valuesArray = new ContentValues[size];
            valuesArray = mValues.toArray(valuesArray);
            mProvider.bulkInsert(mBaseUri, valuesArray);
            mValues.clear();
        }
    }
}
