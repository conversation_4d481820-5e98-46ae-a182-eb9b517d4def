/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: TimeSetUtils
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: W9013333(v-z<PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/29 1.0 create
 */

package com.soundrecorder.common.utils

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil

@Suppress("DEPRECATION")
class TimeSetUtils(private val lifecycleOwner: LifecycleOwner, private var listener: ((String) -> Unit)?) : DefaultLifecycleObserver {

    companion object {
        private const val TAG = "TimeSetUtils"
    }

    init {
        lifecycleOwner.lifecycle.addObserver(this)
        DebugUtil.i(TAG, "init")
    }

    private val broadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val action = intent?.action
            DebugUtil.i(TAG, "action = $action")
            if (action == Intent.ACTION_DATE_CHANGED || action == Intent.ACTION_TIME_CHANGED) {
                listener?.invoke(action)
            }
        }
    }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        BaseApplication.getApplication()?.registerReceiver(broadcastReceiver, IntentFilter().apply {
            addAction(Intent.ACTION_DATE_CHANGED)
            addAction(Intent.ACTION_TIME_CHANGED)
        })
        DebugUtil.i(TAG, "onCreate")
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        listener = null
        BaseApplication.getApplication()?.unregisterReceiver(broadcastReceiver)
        lifecycleOwner.lifecycle.removeObserver(this)
        DebugUtil.i(TAG, "onDestroy")
    }
}