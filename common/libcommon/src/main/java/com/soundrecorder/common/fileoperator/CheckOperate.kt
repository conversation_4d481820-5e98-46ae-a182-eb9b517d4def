package com.soundrecorder.common.fileoperator

import android.app.Activity
import android.net.Uri
import android.os.Build
import androidx.annotation.RequiresApi
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.fileoperator.rename.NameFileDialogUtil
import java.lang.ref.WeakReference

class CheckOperate : CheckPermissionBeforeOperate.Callback {
    @Volatile
    var mOperating = false

    private var mActivityRef: WeakReference<Activity>? = null
    private var mOperateStatus = -1
    private var mDeleteUris: List<Uri>? = null
    private var mDeleteRequestCode: Int? = null
    private var mRenameUri: Uri? = null
    private var mNewName: String? = null
    private var mSuffix: String? = null
    private var mMimeType: String? = null
    private var mNameFileDialogUtil: NameFileDialogUtil? = null

    private var mRecoverUris: List<Uri>? = null
    private var mRecoverRequestCode: Int? = null

    companion object {
        private const val TAG = "CheckOperate"
        const val OPERATE_DELETE = 0
        const val OPERATE_RENAME = 1
        const val OPERATE_RECOVER = 2
        const val OPERATE_SMART_NAME = 3
    }

    fun setRenameUri(
        uri: Uri?,
        newName: String?,
        suffix: String?,
        mimeType: String?,
        nameFileDialogUtil: NameFileDialogUtil
    ) {
        mRenameUri = uri
        mNewName = newName
        mSuffix = suffix
        mNameFileDialogUtil = nameFileDialogUtil
        mMimeType = mimeType
    }

    fun getRenameContent(): String? = mNewName

    fun setDeleteUris(deleteUris: List<Uri>, deleteRequestCode: Int? = null): CheckOperate {
        mDeleteUris = deleteUris
        mDeleteRequestCode = deleteRequestCode
        return this
    }

    fun setRecoverUris(recoverUris: List<Uri>, recoverRequestCode: Int? = null): CheckOperate {
        mRecoverUris = recoverUris
        mRecoverRequestCode = recoverRequestCode
        return this
    }

    fun setOperateStatus(operateStatus: Int): CheckOperate {
        mOperateStatus = operateStatus
        return this
    }

    fun getOperateStatus(): Int = mOperateStatus

    fun setActivity(mActivity: Activity?): CheckOperate {
        mActivityRef = WeakReference(mActivity)
        return this
    }

    override fun onNext() {
        val activity = mActivityRef?.get()
        if (activity == null) {
            DebugUtil.d(TAG, "CheckOperate activity is null")
            return
        }
        mOperating = true
    }

    @RequiresApi(Build.VERSION_CODES.R)
    override fun onCancel() {
        val activity = mActivityRef?.get()
        if (activity == null) {
            DebugUtil.d(TAG, "CheckOperate activity is null")
            return
        }

        try {
            DebugUtil.d(TAG, "CheckOperate $mOperateStatus")
            when (mOperateStatus) {
                OPERATE_DELETE ->  {
                    //FileDealUtil.delete(activity, mDeleteUris, mDeleteRequestCode)
                    DebugUtil.d(TAG, "CheckOperate OPERATE_DELETE")
                }
                OPERATE_RENAME -> {
                    mNameFileDialogUtil?.rename(
                        activity, mRenameUri, mNewName, mSuffix, mMimeType, false)
                }
                OPERATE_RECOVER -> {
                    //恢复录音文件
                    DebugUtil.d(TAG, "recover record")
                }
            }
        } catch (e: Throwable) {
            DebugUtil.e(TAG, "callback exception", e)
        }
    }
}