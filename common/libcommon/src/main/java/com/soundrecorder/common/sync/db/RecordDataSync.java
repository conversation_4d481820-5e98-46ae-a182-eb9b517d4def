/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: RecordDataSync
 ** Description: sync records from MediaDB to Recorder DB
 ** Version: 1.0
 ** Date : 2019-07-11
 ** Author: huangyuanwang
 **
 ** v1.0, 2019-3-12, huangyuanwang, create
 ****************************************************************/
// OPLUS Java File Skip Rule:MethodLength,LineLength

package com.soundrecorder.common.sync.db;

import static android.content.ContentResolver.QUERY_ARG_LIMIT;
import static android.content.ContentResolver.QUERY_ARG_OFFSET;
import static android.content.ContentResolver.QUERY_ARG_SQL_SELECTION;
import static android.content.ContentResolver.QUERY_ARG_SQL_SELECTION_ARGS;
import static android.content.ContentResolver.QUERY_ARG_SQL_SORT_ORDER;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_PRIVETE_ENCRYPT;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_RECOVERY_MEGADATA_SUC;
import static com.soundrecorder.common.constant.RecordConstant.SYNC_TYPE_RECOVERY;

import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.os.SystemClock;
import android.provider.MediaStore;
import android.text.TextUtils;

import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.MD5Utils;
import com.soundrecorder.base.utils.PrefUtil;
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.constant.DatabaseConstant;
import com.soundrecorder.common.constant.DatabaseConstant.RecordUri;
import com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn;
import com.soundrecorder.common.constant.RecordConstant;
import com.soundrecorder.common.constant.RecordModeConstant;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.db.ConvertDeleteUtil;
import com.soundrecorder.common.db.CursorHelper;
import com.soundrecorder.common.db.GroupInfoManager;
import com.soundrecorder.common.db.MediaDBUtils;
import com.soundrecorder.common.db.RecorderDBUtil;
import com.soundrecorder.common.permission.PermissionUtils;
import com.soundrecorder.common.share.OShareConvertUtil;
import com.soundrecorder.common.sync.encryptbox.EncryptBoxConstant;
import com.soundrecorder.common.sync.encryptbox.EncryptBoxProviderQueryUtil;
import com.soundrecorder.common.utils.FunctionOption;
import com.soundrecorder.common.utils.RecordFileChangeNotify;
import com.soundrecorder.common.utils.RecordModeUtil;
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface;
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant;
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface;
import com.soundrecorder.modulerouter.utils.KoinInterfaceHelper;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class RecordDataSync {
    private static final String TAG = "RecordDataSync";
    private static final int SQL_MAX_QUERY_NUMBER = 500;
    private static final long MSEC_PER_SECONDS = 1000;
    private static final int MEDIA_DB_ACCESS_DELAY_TIME = 10; //以前这里写的500，先优化成10ms观察
    private static final long MONTH_IN_MS = 30 * 24 * 60 * 60 * 1000L;
    private static final String CONCATE_PROJECTION_STRING = MediaStore.Images.Media.RELATIVE_PATH + " || " + MediaStore.Images.Media.DISPLAY_NAME;
    /**
     * 云同步支持mimeType，内销仅支持 mp3 amr
     */
    private static final String[] SUPPORT_MIME_TYPE = new String[]{
            RecordConstant.MIMETYPE_MP3,
            RecordConstant.MIMETYPE_AMR,
            RecordConstant.MIMETYPE_AMR_WB
    };
    private static volatile RecordDataSync sInstance;
    private static volatile boolean sStopDiff = false;
    private final CloudKitInterface mCloudKitApi = KoinInterfaceHelper.INSTANCE.getCloudKitApi();
    private final RecorderServiceInterface mRecorderViewModelApi = KoinInterfaceHelper.INSTANCE.getRecorderViewModelApi();
    private boolean mHasRenamed = false;
    private RecordFileChangeNotify mRecordFileChangeNotify = new RecordFileChangeNotify();

    private volatile boolean mMediaComparing = false;

    public static RecordDataSync getInstance() {
        if (sInstance == null) {
            synchronized (RecordDataSync.class) {
                if (sInstance == null) {
                    sInstance = new RecordDataSync();
                }
            }
        }

        return sInstance;
    }

    public static List<Record> getMediaData(Context context, Uri uri, String selection, String[] selectionArgs, int
            limitStart, int limitEnd) {
        long time = System.currentTimeMillis();
        List<Record> mediaData = new ArrayList<Record>();
        String order = null;
        if (BaseUtil.isAndroidQOrLater()) {
            order = MediaStore.Images.Media.RELATIVE_PATH + ", " + MediaStore.Images.Media.DISPLAY_NAME;
        } else {
            order = MediaStore.Images.Media.DATA;
        }
        order += " COLLATE NOCASE";
        Integer limit = null;
        Integer offset = null;
        if (limitEnd != -1) {
            limit = limitEnd - limitStart;
            offset = limitStart;
        }
        DebugUtil.i(TAG, "getMediaData: selection: " + selection + ", order: " + order + ", limit : " + limit + ", offset: " + offset);
        Bundle sqlSelectionArgs = createSqlQueryBundle(selection, selectionArgs, order, limit, offset);
        Cursor cursor = null;
        try {
            ContentResolver resolver = context.getContentResolver();
            cursor = resolver.query(uri, null, sqlSelectionArgs, null);
            if (cursor == null) {
                return null;
            }

            Record recordFile = null;
            while (cursor.moveToNext()) {
                if (sStopDiff) {
                    DebugUtil.i(TAG, "getMediaData mStopDiff ");
                    return null;
                }
                recordFile = new Record(cursor, Record.TYPE_FROM_MEDIA);
                if (recordFile.isInvalidOriginalPath()) {
                    DebugUtil.e(TAG, "getMediaData, invalid path = " + recordFile.getDisplayName());
                } else {
                    mediaData.add(recordFile);
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "getMediaData, e=", e);
            return null;
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        DebugUtil.d(TAG, "getMediaData, size=" + mediaData.size() + ", cost time=" + (System.currentTimeMillis() - time));
        return mediaData;
    }


    public void setStopDiff(boolean stopDiff) {
        setsStopDiff(stopDiff);
        this.mMediaComparing = false;
        DebugUtil.i(TAG, "setStopDiff: " + stopDiff + ", media comparing STOP");
    }

    private static synchronized void setsStopDiff(boolean stopDiff) {
        sStopDiff = stopDiff;
    }

    public boolean isMediaComparing() {
        return this.mMediaComparing;
    }

    public synchronized void syncAllRecordDataFromMedia(Context context, boolean isNeedCheckRename, boolean trigCloudSyncRightNow, int syncType) {
        if (context == null) {
            DebugUtil.d(TAG, "syncAllRecordDataFromMedia, finish, context is null");
            return;
        }

        if (!PermissionUtils.hasReadAudioPermission()) {
            DebugUtil.d(TAG, "syncAllRecordDataFromMedia, finish, reason no storage permission");
            return;
        }

        mHasRenamed = false;
        setsStopDiff(false);
        mMediaComparing = true;
        DebugUtil.d(TAG, "syncAllRecordDataFromMedia, start, trigCloudSyncRightNow: "
                + trigCloudSyncRightNow + ", syncType: "
                + syncType + ", isNeedCheckRename: " + isNeedCheckRename + ",mediaComparing:" + mMediaComparing);
        CloudStaticsUtil.addCloudLog(TAG, "syncAllRecordDataFromMedia,start.trigNow="
                + trigCloudSyncRightNow + ",syncType=" + syncType + ",mediaComparing:" + mMediaComparing);
        long time = SystemClock.elapsedRealtime();
        //sync standard record files
        syncDataMedia(context, isNeedCheckRename, true, RecordModeConstant.RECORD_TYPE_STANDARD);
        //sync meeting record files
        syncDataMedia(context, isNeedCheckRename, true, RecordModeConstant.RECORD_TYPE_CONFERENCE);
        //sync interview record files
        syncDataMedia(context, isNeedCheckRename, true, RecordModeConstant.RECORD_TYPE_INTERVIEW);
        //sync call record files
        syncDataMedia(context, isNeedCheckRename, true, RecordModeConstant.RECORD_TYPE_CALL);
        //sync other record files
        syncDataMedia(context, isNeedCheckRename, true, RecordModeConstant.RECORD_TYPE_OTHER);
        //sync OShare record files
        syncDataMedia(context, isNeedCheckRename, true, RecordModeConstant.RECORD_TYPE_OPPO_SHARE);
        if (sStopDiff) {
            DebugUtil.i(TAG, "syncAllRecordDataFromMedia mStopDiff ");
            mMediaComparing = false;
            return;
        }
        GroupInfoManager.getInstance(context).verifyGroupCount();

        SharedPreferences mSp = PrefUtil.getSharedPreferences(context);
        if (mSp != null && !sStopDiff) {
            mSp.edit().putLong(PrefUtil.KEY_LAST_SYNC_TIME, System.currentTimeMillis()).commit();
        }
        CloudStaticsUtil.addCloudLog(TAG, "syncAllRecordDataFromMedia, end.trigCloudSyncRightNow=" + trigCloudSyncRightNow);
        DebugUtil.d(TAG, "syncAllRecordDataFromMedia, end. cost time=" + (SystemClock.elapsedRealtime() - time));
        mMediaComparing = false;
        trigCloudSync(context, trigCloudSyncRightNow, syncType);
        if (mHasRenamed) {
            mRecordFileChangeNotify.notifyBySendBroadcast(context);
            mHasRenamed = false;
        }
        //同步结束后再开始联系人头像颜色的数据库更新
        sendUpdateCallerNameAvatarColorLocalBroadcast(RecorderDataConstant.START_UPDATE_CALLER_NAME_AVATAR_COLOR);
    }

    private void sendUpdateCallerNameAvatarColorLocalBroadcast(int flag) {
        Intent intentReceiver = new Intent(RecorderDataConstant.ACTION_UPDATE_CALLER_NAME_AVATAR_COLOR);
        intentReceiver.putExtra(RecorderDataConstant.UPDATE_CALLER_NAME_AVATAR_COLOR_VALUE, flag);
        DebugUtil.d(TAG, "sendUpdateCallerNameAvatarColorLocalBroadcast:" + RecorderDataConstant.ACTION_UPDATE_CALLER_NAME_AVATAR_COLOR);
        LocalBroadcastManager.getInstance(BaseApplication.getAppContext()).sendBroadcast(intentReceiver);
    }

    private void trigCloudSync(Context context, boolean trigCloudSyncRightNow, int syncType) {
        if (trigCloudSyncRightNow && mCloudKitApi != null) {
            mCloudKitApi.trigCloudSync(context, syncType);
        }
    }

    private void syncDataMedia(Context context, boolean isNeedCheckRename, boolean syncAll, int recordType) {
        if (sStopDiff) {
            DebugUtil.i(TAG, "syncDataMedia mStopDiff ");
            return;
        }
        DebugUtil.d(TAG, "syncDataMedia start,recordType = " + recordType);
        long time = System.currentTimeMillis();
        boolean isFirstLoop = true;
        boolean isCallRecords = (recordType == RecordModeConstant.RECORD_TYPE_CALL);
        SqlLimit limitStart = null;
        SqlLimit limitEnd = null;
        SqlLimit firstLimitStart = null;
        SqlLimit lastLimitEnd = null;
        String selection = null;
        List<Record> mediaFiles = null;
        List<String> argsList = new ArrayList<String>();
        String whereClause = CursorHelper.getWhereClauseFromRecorderTypeForSyncMediaAudioFiles(context, recordType, getSupportSyncMimeType());

        if (!syncAll) {
            SharedPreferences mSp = PrefUtil.getSharedPreferences(context);
            if (mSp != null) {
                long lastSyncTime = mSp.getLong(PrefUtil.KEY_LAST_SYNC_TIME, 0L);
                whereClause += " and " + MediaStore.Audio.Media.DATE_ADDED + " >= " + lastSyncTime;
            }
        }

        do {
            if (sStopDiff) {
                DebugUtil.i(TAG, "syncDataMedia do mStopDiff ");
                return;
            }

            argsList.clear();

            if (limitStart == null) {
                selection = whereClause;
            } else {
                if (BaseUtil.isAndroidQOrLater()) {
                    selection = whereClause + " AND (LOWER(" + CONCATE_PROJECTION_STRING + ") > LOWER(?))";
                    argsList.add(limitStart.getRelativePath() + limitStart.getDisplayName());
                } else {
                    selection = whereClause + " AND (LOWER(" + MediaStore.Images.Media.DATA + ") > LOWER(?))";
                    argsList.add(limitStart.getData());
                }
            }

            String fileBeingRecorded = "";
            if (mRecorderViewModelApi != null) {
                fileBeingRecorded = mRecorderViewModelApi.getFileBeingRecorded();
            }
            if (!TextUtils.isEmpty(fileBeingRecorded)) {
                selection += " and " + MediaStore.Audio.Media.DATA + " != ?";
                argsList.add(fileBeingRecorded);
            }

            String[] selectionArgs = new String[argsList.size()];
            argsList.toArray(selectionArgs);
            if (isCallRecords) {
                mediaFiles = getMediaData(context, MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, selection,
                        selectionArgs, 0, Constants.PAGE_SIZE);
            } else {
                mediaFiles = getMediaData(context, MediaDBUtils.BASE_URI, selection, selectionArgs, 0, Constants.PAGE_SIZE);
            }
            if ((mediaFiles == null) || mediaFiles.isEmpty()) {
                DebugUtil.w(TAG, "syncDataMedia, mediaFiles is null");
                break;
            }
            DebugUtil.d(TAG, "getMediaData, selection = " + selection + ", size=" + mediaFiles.size());
            Record firstMediaRecord = mediaFiles.get(0);
            if (isFirstLoop && (firstMediaRecord != null)) {
                if (BaseUtil.isAndroidQOrLater()) {
                    firstLimitStart = new SqlLimit(firstMediaRecord.getRelativePath(), firstMediaRecord.getDisplayName());
                } else {
                    firstLimitStart = new SqlLimit(firstMediaRecord.getData());
                }
                isFirstLoop = false;
            }
            Record lastMediaRecord = mediaFiles.get(mediaFiles.size() - 1);
            if (lastMediaRecord != null) {
                if (BaseUtil.isAndroidQOrLater()) {
                    limitEnd = new SqlLimit(lastMediaRecord.getRelativePath(), lastMediaRecord.getDisplayName());
                } else {
                    limitEnd = new SqlLimit(lastMediaRecord.getData());
                }
            }

            doDiffMediaRecord(context, mediaFiles, limitStart, limitEnd, recordType, isNeedCheckRename, syncAll);

            if (limitEnd != null) {
                limitStart = limitEnd;
                lastLimitEnd = limitEnd;
            }

            DebugUtil.d(TAG, "syncDataMedia, limitEnd=" + limitEnd + ", size=" + mediaFiles.size());

            if (mediaFiles.size() > 0 && !sStopDiff) {
                queryMediaSleep();
            }
        } while ((mediaFiles.size() > 0) && !sStopDiff);

        // 文件管理下数据为空，本地数据库不一定为null
        processRemaidDbRecords(context, firstLimitStart, lastLimitEnd, recordType, syncAll, isNeedCheckRename);
        DebugUtil.d(TAG, "syncDataMedia end recordType = " + recordType + " ,cost time=" + (System.currentTimeMillis() - time));
    }


    private void processRemaidDbRecords(Context context, SqlLimit limitStart, SqlLimit limitEnd, int recordType, boolean syncAll, boolean needRename) {
        if (sStopDiff) {
            DebugUtil.i(TAG, "processRemaidDbRecords mStopDiff ");
            return;
        }
        List<Record> deleteData = new ArrayList<Record>();
        List<Record> updateData = new ArrayList<Record>();
        List<Record> remaindRecordsInDb = getRemaindRecordsInDb(context, limitStart, recordType, syncAll);
        if ((remaindRecordsInDb != null) && (remaindRecordsInDb.size() > 0)) {
            for (Record record : remaindRecordsInDb) {
                if (sStopDiff) {
                    DebugUtil.i(TAG, "processRemaidDbRecords for mStopDiff ");
                    return;
                }
                checkEncryptBoxDataAndProcessRemainderFiles(context, record, updateData, deleteData, false);
            }
        }
        CloudStaticsUtil.addCloudLog(TAG, "processRemaidDbRecords, updateData.size=" + updateData.size() + ",deleteData.size=" + deleteData.size());
        processDeleteData(context, deleteData);
        processUpdateData(context, updateData);
    }

    private List<Record> getRemaindRecordsInDb(Context context, SqlLimit limitStart, int recordType, boolean syncAll) {
        List<String> argsList = new ArrayList<String>();
        String selection = null;
        String whereClause = RecorderDBUtil.getWhereClauseFromRecorderType(context, recordType, getSupportSyncMimeType());
        if (!syncAll) {
            SharedPreferences mSp = PrefUtil.getSharedPreferences(context);
            if (mSp != null) {
                long lastSyncTime = mSp.getLong(PrefUtil.KEY_LAST_SYNC_TIME, 0L);
                whereClause += " and " + MediaStore.Audio.Media.DATE_ADDED + " >= " + lastSyncTime;
            }
        }
        if (limitStart != null) {
            if (BaseUtil.isAndroidQOrLater()) {
                selection = whereClause + " AND (LOWER(" + DatabaseConstant.CONCAT_PROJECTION_STRING
                        + ") <= LOWER(?) AND LOWER(" + DatabaseConstant.CONCAT_PROJECTION_STRING + ") > LOWER(?))";
                argsList.add(limitStart.getRelativePath() + limitStart.getDisplayName());
            } else {
                selection = whereClause + " AND (LOWER(" + RecorderColumn.COLUMN_NAME_DATA
                        + ") <= LOWER(?) AND LOWER(" + RecorderColumn.COLUMN_NAME_DATA + ") > LOWER(?))";
                argsList.add(limitStart.getData());
            }
        } else {
            DebugUtil.i(TAG, "getRemaindRecordsInDb: recordType is " + recordType + "limitStart is null");
        }

        String[] selectionArgs = new String[argsList.size()];
        argsList.toArray(selectionArgs);

        String order = null;
        if (BaseUtil.isAndroidQOrLater()) {
            order = RecorderColumn.COLUMN_NAME_RELATIVE_PATH + ", " + RecorderColumn.COLUMN_NAME_DISPLAY_NAME;
        } else {
            order = RecorderColumn.COLUMN_NAME_DATA;
        }
        order += " COLLATE NOCASE";
        Uri uri = RecordUri.RECORD_CONTENT_URI;
        List<Record> remaindRecordDatas = getRecordData(context, uri, null, selection, selectionArgs,
                order, null);
        DebugUtil.i(TAG, "get remaind record size(): " + ((remaindRecordDatas != null) ? remaindRecordDatas.size() : 0));
        return remaindRecordDatas;
    }


    /**
     * Access to the media library takes too long for files around 100 thousand.
     */
    private void queryMediaSleep() {
        try {
            Thread.sleep(MEDIA_DB_ACCESS_DELAY_TIME);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private void doDiffMediaRecord(Context context, List<Record> mediaFiles, SqlLimit limitStart, SqlLimit limitEnd,
                                   int recordType, boolean isNeedCheckRename, boolean syncAll) {
        DebugUtil.d(TAG, "doDiffMediaRecord, start. recordType = " + recordType);
        if (sStopDiff) {
            DebugUtil.i(TAG, "doDiffMediaRecord mStopDiff ");
            return;
        }
        if (mediaFiles.size() > 0) {
            String whereClause = RecorderDBUtil.getWhereClauseFromRecorderType(context, recordType, getSupportSyncMimeType());
            List<String> argsList = new ArrayList<String>();

            List<Record> recordData = null;

            if (limitStart != null) {
                if (limitStart.isQType()) {
                    whereClause += (" AND " + "LOWER(" + DatabaseConstant.CONCAT_PROJECTION_STRING + ") > " + "LOWER(?)");
                    argsList.add(limitStart.getRelativePath() + limitStart.getDisplayName());
                } else {
                    whereClause += (" AND " + "LOWER(" + RecorderColumn.COLUMN_NAME_DATA + ") > " + "LOWER(?)");
                    argsList.add(limitStart.getData());
                }
            }

            if (limitEnd != null) {
                if (limitEnd.isQType()) {
                    whereClause += (" AND " + "LOWER(" + DatabaseConstant.CONCAT_PROJECTION_STRING + ") <= " + "LOWER(?)");
                    argsList.add(limitEnd.getRelativePath() + limitEnd.getDisplayName());
                } else {
                    whereClause += (" AND " + "LOWER(" + RecorderColumn.COLUMN_NAME_DATA + ") <= " + "LOWER(?)");
                    argsList.add(limitEnd.getData());
                }
            }

            if (!syncAll) {
                SharedPreferences mSp = PrefUtil.getSharedPreferences(context);
                if (mSp != null) {
                    long lastSyncTime = mSp.getLong(PrefUtil.KEY_LAST_SYNC_TIME, 0L);
                    whereClause += " and " + RecorderColumn.COLUMN_NAME_DATE_MODIFIED + " >= " + lastSyncTime;
                }
            }

            String[] selectionArgs = new String[argsList.size()];
            argsList.toArray(selectionArgs);
            DebugUtil.d(TAG, "record data whereClause:" + whereClause);
            Uri uri = RecordUri.RECORD_CONTENT_URI;
            recordData = getRecordData(context, uri, null, whereClause, selectionArgs,
                    null, mediaFiles);
            if (isNeedCheckRename) {
                diffMediaRecordAndCheckRename(context, mediaFiles, recordData);
            } else {
                diffMediaRecord(context, recordType, mediaFiles, recordData);
            }
        }

        DebugUtil.d(TAG, "doDiffMediaRecord, end. recordType = " + recordType);
    }

    private boolean checkMd5AndDisplayName(List<Record> srcList, Record record) {
        if (srcList != null && !srcList.isEmpty() && record != null) {
            for (Record tmpRecord : srcList) {
                if (tmpRecord != null && tmpRecord.sameMD5(record) && tmpRecord.sameDisplayName(record)) {
                    return true;
                }
            }
        }
        return false;
    }

    private void processRecordRemainder(Context context, List<Record> updateData, List<Record> deleteData, Record fileRecord) {
        // file not exist , fileRecord is not waiting download
        if (fileRecord.fileNotExist()) {
            // the record file deleted in FileExplorer , but the record in DB is still exist , here set the fileRecord to deleted
            if (fileRecord.hasGlobalId() && (fileRecord.getSyncDownlodStatus() <= 0)) {
                fileRecord.setDeleted(true);
                updateData.add(fileRecord);
                DebugUtil.d(TAG, "processRecordRemainder, delete file2=" + fileRecord.getDisplayName());
            } else {
                // The file is not uploaded and the file does not exist. Delete this entry.
                DebugUtil.d(TAG, "processRecordRemainder, file not exist, delete item : " + fileRecord);
                deleteData.add(fileRecord);
            }
        }
    }


    private void processRecordRemainderNotDeleteOnServer(Context context, List<Record> updateData, List<Record> deleteData, Record fileRecord) {
        // file not exist , fileRecord is not waiting download
        if (fileRecord.fileNotExist()) {
            // the record file deleted in FileExplorer , but the record in DB is still exist , here set the fileRecord to deleted
            if (fileRecord.hasGlobalId()) {
                //set wait to download
                if (fileRecord.hasFileId() && (fileRecord.getSyncDownlodStatus() <= 0)) {
                    fileRecord.setSyncType(SYNC_TYPE_RECOVERY);
                    fileRecord.setSyncDownlodStatus(SYNC_STATUS_RECOVERY_MEGADATA_SUC);
                    updateData.add(fileRecord);
                }
                DebugUtil.d(TAG, "processRecordRemainderNotDeleteOnServer, update SyncDownloadStatus =" + fileRecord.getDisplayName());
            } else {
                // The file is not uploaded and the file does not exist. Delete this entry.
                DebugUtil.d(TAG, "processRecordRemainderNotDeleteOnServer, file not exist, delete item : " + fileRecord);
                deleteData.add(fileRecord);
            }
        }
    }

    private void diffMediaRecord(Context context, int recordType, List<Record> mediaData, List<Record> recordData) {
        DebugUtil.d(TAG, "diffMediaRecord start");
        long time = System.currentTimeMillis();
        List<Record> insertData = new ArrayList<Record>();
        List<Record> deleteData = new ArrayList<Record>();
        List<Record> updateData = new ArrayList<Record>();

        if ((recordData == null) || recordData.isEmpty()) { //recorder.db is empty
            if (mediaData.size() > 0) {
                insertData.addAll(mediaData);
                processInsertData(context, insertData);
            }
        } else if (!mediaData.isEmpty()) { //both is not empty
            int sizeMedia = mediaData.size();
            int sizeRecord = recordData.size();
            DebugUtil.d(TAG, "diffMediaRecord, sizeMedia=" + sizeMedia + ", sizeRecord=" + sizeRecord);

            String keyMedia = "";
            String keyRecord = "";
            Record fileMedia = null;
            Record fileRecord = null;
            int i = 0;
            int j = 0;

            for (; i < sizeMedia; i++) {
                if (sStopDiff) {
                    DebugUtil.i(TAG, "diffMediaRecord for mStopDiff ");
                    return;
                }
                fileMedia = mediaData.get(i);
                keyMedia = getKeyForRecord(fileMedia);
                // All recordData has been traversaled, then last mediaData were newly added.
                if (j > sizeRecord - 1) {
                    insertData.add(fileMedia);
                } else {
                    for (; j < sizeRecord; j++) {
                        if (sStopDiff) {
                            DebugUtil.i(TAG, "diffMediaRecord for 2 mStopDiff ");
                            return;
                        }
                        fileRecord = recordData.get(j);
                        keyRecord = getKeyForRecord(fileRecord);
                        int result = keyMedia.compareToIgnoreCase(keyRecord);
                        if (result == 0) { //same directory and same file name
                            diffMediaProcessEqualsCase(context, fileMedia, fileRecord, insertData, updateData, deleteData);
                            j++;
                            break;
                        } else if (result > 0) { //check whether need to delete this data from recorder.db
                            if (j == (sizeRecord - 1)) {
                                insertData.add(fileMedia);
                                checkEncryptBoxDataAndProcessRemainderFiles(context, fileRecord, updateData, deleteData, false);
                                j++;
                                break;
                            }
                            checkEncryptBoxDataAndProcessRemainderFiles(context, fileRecord, updateData, deleteData, false);
                        } else { //in this case, insert media data to recorder.db
                            insertData.add(fileMedia);
                            checkEncryptBoxDataAndProcessRemainderFiles(context, fileRecord, updateData, deleteData, false);
                            break;
                        }
                    }
                }
            }

            DebugUtil.d(TAG, "diffMediaRecord, galleryData index=" + j);
            // All mediaData has been traversaled, then last galleryData were deleted.
            for (; j < sizeRecord; j++) {
                if (sStopDiff) {
                    DebugUtil.i(TAG, "diffMediaRecord for sizeRecord mStopDiff ");
                    return;
                }
                checkEncryptBoxDataAndProcessRemainderFiles(context, recordData.get(j), updateData, deleteData, false);
            }

            processDeleteData(context, deleteData);
            processUpdateData(context, updateData);
            processInsertData(context, insertData);
        } else if (recordData.size() > 0) { // only media is empty
            DebugUtil.d(TAG, "only media is empty, and recorder.db is not empty");
            for (Record file : recordData) {
                checkEncryptBoxDataAndProcessRemainderFiles(context, file, updateData, deleteData, false);
            }

            processUpdateData(context, updateData);
            processDeleteData(context, deleteData);
        }
        DebugUtil.d(TAG, "diffMediaRecord, cost time=" + (System.currentTimeMillis() - time));
        CloudStaticsUtil.addCloudLog(TAG, "processMediaRecord,type=" + recordType
                + ",insertData.size=" + insertData.size() + ",updateData.size=" + updateData.size() + ",deleteData.size=" + deleteData.size());
    }

    private void diffMediaProcessEqualsCase(Context context, Record fileMedia, Record fileRecord, List<Record> insertData,
                                            List<Record> updateData, List<Record> deleteData) {
        if (fileMedia.fileNotExist()) {
            // The file has been deleted, but the media library has not yet updated
            // the data and is waiting for the next synchronization.
            DebugUtil.v(TAG, "diffMediaProcessEqualsCase, media db has data, but file not exist.");
            checkEncryptBoxDataAndProcessRemainderFiles(context, fileRecord, updateData, deleteData, false);
        } else {
            if (fileMedia.sameAddedTime(fileRecord) && fileMedia.sameFileSize(fileRecord)
                    && fileMedia.sameMD5(fileRecord)) { // is same file
                Record updateRecord = fileRecord.constructDBRecordFromMediaRecord(fileMedia);
                updateData.add(updateRecord);
            } else { //is new file
                DebugUtil.d(TAG, "fileMedia.mDisplayName = " + fileMedia.getDisplayName() + " ,fileRecord.mDisplayName = " + fileRecord.getDisplayName()
                        + " ,fileMedia.mDateCreated = " + fileMedia.mDateCreated + " ,fileRecord.mDateCreated = " + fileRecord.mDateCreated
                        + " ,fileMedia.mFileSize = " + fileMedia.mFileSize + " ,fileRecord.mFileSize = " + fileRecord.mFileSize
                        + " ,fileMedia.mMD5 = " + fileMedia.mMD5 + " ,fileRecord.mMD5 = " + fileRecord.mMD5);
                if (fileMedia.sameMD5(fileRecord)) {
                    Record updateRecord = fileRecord.constructDBRecordFromMediaRecord(fileMedia);
                    updateData.add(updateRecord);
                    DebugUtil.i(TAG, "same MD5 fileRecord: " + fileRecord + "\n, updateRecord: " + updateRecord);
                } else if (fileMedia.rename()) {
                    DebugUtil.d(TAG, "diffMediaProcessEqualsCase is new file, " + fileMedia);
                    checkEncryptBoxDataAndProcessRemainderFiles(context, fileRecord, updateData, deleteData, false);
                    insertData.add(fileMedia);
                    mHasRenamed = true;
                    int recordType = RecordModeUtil.getRecordTypeForMediaRecord(fileMedia);
                    if (mRecordFileChangeNotify != null) {
                        mRecordFileChangeNotify.setFileChanged(recordType);
                    }
                }
            }
        }
    }

    private List<Record> getRecordData(Context context, Uri uri, String[] projection, String selection,
                                       String[] selectionArgs, String sortOrder, List<Record> mediaFilesData) {
        long time = System.currentTimeMillis();
        List<Record> recordData = new ArrayList<Record>();

        if (TextUtils.isEmpty(sortOrder)) {
            if (BaseUtil.isAndroidQOrLater()) {
                sortOrder = RecorderColumn.COLUMN_NAME_RELATIVE_PATH + ", " + RecorderColumn.COLUMN_NAME_DISPLAY_NAME + " COLLATE NOCASE" + " LIMIT 0, " + Constants.PAGE_SIZE;
            } else {
                sortOrder = RecorderColumn.COLUMN_NAME_DATA + " COLLATE NOCASE" + " LIMIT 0, " + Constants.PAGE_SIZE;
            }
        }

        DebugUtil.i(TAG, "getRecordData: selection: " + selection + ", order: " + sortOrder);
        Cursor cursor = null;
        try {
            if (uri == null) {
                uri = RecordUri.RECORD_CONTENT_URI;
            }

            ContentResolver resolver = context.getContentResolver();
            cursor = resolver.query(uri, projection, selection, selectionArgs, sortOrder);

            if (cursor == null) {
                DebugUtil.w(TAG, "getRecordData, record cursor is null.");
                return null;
            }

            while (cursor.moveToNext()) {
                if (sStopDiff) {
                    DebugUtil.i(TAG, "getRecordData mStopDiff while");
                   return null;
                }
                Record record = new Record(cursor, Record.TYPE_FROM_RECORD);
                //回收站记录的数据和媒体库的数据对比一下，如果是相同文件，就要返回记录，避免产生新的脏数据
                if (record.isRecycle()) {
                    if (!checkMd5AndDisplayName(mediaFilesData, record)) {
                        continue;
                    }
                }
                recordData.add(record);
            }

            DebugUtil.d(TAG, "getRecordData, recordData.size=" + recordData.size());
        } catch (Exception e) {
            DebugUtil.e(TAG, "getRecordData, e=" + e);
            return null;
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        DebugUtil.d(TAG, "getRecordData, size=" + recordData.size() + " ,cost time=" + (System.currentTimeMillis() - time));
        return recordData;
    }

    /**
     * Split the contact name according to displayName and set the call recording contact name
     *
     * @param record
     * @param recordType
     */
    private static void setRecordCallerName(Record record, int recordType) {
        if (recordType == RecordModeConstant.RECORD_TYPE_CALL && !TextUtils.isEmpty(record.getDisplayName())) {
            String callerName = record.getCallerName();
            //原有联系人名称不能更改，以防按联系人名称分组时数据出错
            if (callerName == null || TextUtils.isEmpty(callerName)) {
                // 可能耗时。
                record.setCallerName(RecorderDBUtil.getCallerName(record, MediaDBUtils.queryIdByData(record.getData())));
            }
            if (record.getOriginalName() == null) {
                // getOriginalName为null，则为初次插入，需要将DisplayName同步进去。
                record.setOriginalName(record.getDisplayName());
            }
        }
    }

    public static void processInsertData(Context context, List<Record> data) {
        if ((data == null) || data.isEmpty()) {
            DebugUtil.d(TAG, "processInSertData record data is empty");
            return;
        }
        if (sStopDiff) {
            DebugUtil.i(TAG, "processInsertData mStopDiff ");
            return;
        }

        long time = System.currentTimeMillis();
        RecordBulkInsert insert = new RecordBulkInsert(context.getContentResolver(), RecordUri.RECORD_CONTENT_URI);

        try {
            int size = data.size();
            DebugUtil.d(TAG, "processInSertData, size =" + size);

            for (Record record : data) {
                if (sStopDiff) {
                    DebugUtil.i(TAG, "processInsertData for mStopDiff ");
                    return;
                }
                String uuID = UUID.randomUUID().toString();
                record.setUuid(uuID);
                record.checkMd5();
                int recordType = RecordModeUtil.getRecordTypeForMediaRecord(record);
                if (TextUtils.isEmpty(record.getRelativePath())) {
                    String relativePath = RecorderDBUtil.getRelativePathForData(record.getData(), record.getDisplayName());
                    record.setRelativePath(relativePath);
                    DebugUtil.i(TAG, "processInSertData: setRelativePath: " + relativePath);
                }

                GroupInfoManager.getInstance(context).tryToInitGroupInfoForRecord(record);
                setRecordCallerName(record, recordType);
                record.setRecordType(recordType);
                record.setSyncType(RecordConstant.SYNC_TYPE_BACKUP);
                record.setSyncUploadStatus(RecordConstant.SYNC_STATUS_BACKUP_START);
                record.setDirty(RecordConstant.RECORD_DIRTY_FILE_AND_MEGA);
                // Oshare目录下的文件将分组设置未普通录音
                if (record.isOShareFile()) {
                    record.setGroupId(OShareConvertUtil.getGroupId());
                    record.setGroupUuid(OShareConvertUtil.getGroupUuid());
                }
                insert.insert(record.convertToContentValues());
            }
        } catch (Throwable t1) {
            DebugUtil.e(TAG, "processInSertData, t1=" + t1);
        } finally {
            try {
                insert.flush();
            } catch (Throwable t2) {
                DebugUtil.e(TAG, "processInSertData, t2=" + t2);
            }
        }

        DebugUtil.v(TAG, "processInSertData, all cost time=" + (System.currentTimeMillis() - time));
    }

    private void processDeleteData(Context context, List<Record> data) {
        if ((data == null) || data.isEmpty()) {
            DebugUtil.d(TAG, "processDeleteData, data is empty.");
            return;
        }
        if (sStopDiff) {
            DebugUtil.i(TAG, "processDeleteData mStopDiff ");
            return;
        }

        DebugUtil.d(TAG, "processDeleteData, data.size=" + data.size());
        RecordBulkDelete delete = new RecordBulkDelete(context.getContentResolver(),
                RecordUri.RECORD_CONTENT_URI, RecorderColumn.COLUMN_NAME_DATA);
        try {
            File file = null;
            int count = 0;

            for (Record record : data) {
                if (sStopDiff) {
                    DebugUtil.i(TAG, "processDeleteData for mStopDiff ");
                    return;
                }
                file = new File(record.mData);
                if (file.exists()) {
                    record.checkMd5();
                    String newMd5 = MD5Utils.getMD5(file);

                    if (newMd5.equals(record.mMD5)) {
                        count++;
                    } else {
                        if (sStopDiff) {
                            DebugUtil.i(TAG, "processDeleteData mStopDiff 2");
                            return;
                        }
                        delete.delete(record.mData);
                    }
                } else {
                    if (sStopDiff) {
                        DebugUtil.i(TAG, "processDeleteData  mStopDiff 3");
                        return;
                    }
                    delete.delete(record.mData);
                }
            }

            DebugUtil.d(TAG, "processDeleteData, file exist. count=" + count);
        } catch (Throwable t1) {
            DebugUtil.e(TAG, "processDeleteData, t1=" + t1);
        } finally {
            try {
                if (sStopDiff) {
                    DebugUtil.i(TAG, "syncDataMedia mStopDiff finally");
                } else {
                    delete.flush();
                }
            } catch (Throwable t2) {
                DebugUtil.e(TAG, "processDeleteData, t2=" + t2);
            }
        }
    }

    private void processUpdateData(Context context, List<Record> data) {
        if ((data == null) || data.isEmpty()) {
            return;
        }
        if (sStopDiff) {
            DebugUtil.i(TAG, "processUpdateData mStopDiff ");
            return;
        }

        DebugUtil.d(TAG, "processUpdateData, data.size=" + data.size());
        long time = System.currentTimeMillis();

        for (Record record : data) {
            if (sStopDiff) {
                DebugUtil.i(TAG, "processUpdateData for mStopDiff ");
                return;
            }
            try {
                String where = RecorderColumn.COLUMN_NAME_DATA + " COLLATE NOCASE =?"
                        + " AND " + RecorderColumn.COLUMN_NAME_MD5 + "=?";
                String[] selectionArgs = new String[]{record.mData, record.mMD5};
                Record dbRecord = null;
                List<Record> dbRecords = RecorderDBUtil.getRecordData(context, null, where, selectionArgs, null);
                if ((dbRecords != null) && (dbRecords.size() >= 1)) {
                    dbRecord = dbRecords.get(0);
                }
                int recordType = RecordModeUtil.getRecordTypeForMediaRecord(record);
                record.setRecordType(recordType);
                if (TextUtils.isEmpty(record.getRelativePath())) {
                    String relativePath = RecorderDBUtil.getRelativePathForData(record.getData(), record.getDisplayName());
                    record.setRelativePath(relativePath);
                }
                setRecordCallerName(record, recordType);
                if (dbRecord != null) {
                    //如果dbRecord的groupUuid为空，则需要初始化分组信息
                    if (sStopDiff) {
                        DebugUtil.i(TAG, "processUpdateData  mStopDiff if");
                        return;
                    }
                    if (TextUtils.isEmpty(dbRecord.getGroupUuid())) {
                        GroupInfoManager.getInstance(context).tryToInitGroupInfoForRecord(record);
                    } else {
                        record.setGroupUuid(dbRecord.getGroupUuid());
                    }
                    boolean noNeedUpdate = checkNoNeedUpdate(dbRecord, record);
                    if (!noNeedUpdate) {
                        record.setSyncType(RecordConstant.SYNC_TYPE_BACKUP);
                        record.setSyncUploadStatus(RecordConstant.SYNC_STATUS_BACKUP_START);
                        record.setDirty(RecordConstant.RECORD_DIRTY_FILE_AND_MEGA);
                    }
                    DebugUtil.e(TAG, "diff media: processUpdateData, name=" + record.getDisplayName() + ",md5 =" + record.getMD5());
                    int updateCount = RecorderDBUtil.updateRecordData(context, record.convertToContentValues(),
                            RecorderColumn.COLUMN_NAME_ID + "=?", new String[]{String.valueOf(dbRecord.getId())});
                }
            } catch (Exception e) {
                DebugUtil.e(TAG, "processUpdateData, e=" + e);
            }
        }

        DebugUtil.v(TAG, "processUpdateData, cost time=" + (System.currentTimeMillis() - time));
    }

    private boolean checkNoNeedUpdate(Record dbRecord, Record mediaRecord) {
        boolean pathEqual = dbRecord.getData().equalsIgnoreCase(mediaRecord.getData());
        boolean sizeEqual = dbRecord.getFileSize() == mediaRecord.getFileSize();
        boolean durationEqual = dbRecord.getDuration() == mediaRecord.getDuration();
        boolean lastModifyTimeEqual = dbRecord.getDateModied() == mediaRecord.getDateModied();
        boolean dbRecordInCloud = (!TextUtils.isEmpty(dbRecord.getGlobalId())) && (!TextUtils.isEmpty(dbRecord.getFileId()));
        boolean dbRecordInEncrypBox = dbRecord.getSyncPrivateStatus() == RECORD_PRIVETE_ENCRYPT;
        DebugUtil.i(TAG, "checkNoNeedUpdate: globalId: " + dbRecord.getGlobalId() + ", fileId: " + dbRecord.getFileId());
        return pathEqual && sizeEqual && durationEqual && lastModifyTimeEqual && dbRecordInCloud && (!dbRecordInEncrypBox);
    }

    private boolean checkRenameMediaFileFromOtherApp(Context context, Record mediaFile) {
        boolean isRename = false;
        if (mediaFile != null) {
            List<Record> recordFiles = RecorderDBUtil.getInstance(context).queryMaybeRenameRecordFiles(mediaFile);
            if (recordFiles != null) {
                for (Record recordFile : recordFiles) {
                    int indexMedia = mediaFile.getData().indexOf(File.separator);
                    int indexRecord = recordFile.getData().indexOf(File.separator);
                    String dirMediaFile = mediaFile.getData().substring(0, indexMedia);
                    String dirRecordFile = recordFile.getData().substring(0, indexRecord);
                    if (dirMediaFile.equals(dirRecordFile)) {
                        recordFile.setDisplayName(mediaFile.getDisplayName());
                        recordFile.setData(mediaFile.getData());
                        recordFile.setSyncType(RecordConstant.SYNC_TYPE_BACKUP);
                        recordFile.setSyncUploadStatus(RecordConstant.SYNC_STATUS_BACKUP_START);
                        recordFile.setDirty(RecordConstant.RECORD_DIRTY_FILE_AND_MEGA);
                        RecorderDBUtil.getInstance(context).updateRenameRecordDB(recordFile);
                        isRename = true;
                    }
                }
            }
        }
        return isRename;
    }


    private void diffMediaRecordAndCheckRename(Context context, List<Record> mediaData, List<Record> recordData) {
        DebugUtil.d(TAG, "diffMediaRecordAndCheckRename start");
        if (sStopDiff) {
            DebugUtil.i(TAG, "diffMediaRecordAndCheckRename mStopDiff ");
            return;
        }
        List<Record> insertData = new ArrayList<Record>();
        List<Record> deleteData = new ArrayList<Record>();
        List<Record> updateData = new ArrayList<Record>();

        if ((recordData == null) || recordData.isEmpty()) { //recorder.db is empty
            if (mediaData.size() > 0) {
                insertData.addAll(mediaData);
                processInsertData(context, insertData);
            }
        } else if (!mediaData.isEmpty()) { //both is not empty
            int sizeMedia = mediaData.size();
            int sizeRecord = recordData.size();
            DebugUtil.d(TAG, "diffMediaRecord, sizeMedia=" + sizeMedia + ", sizeRecord=" + sizeRecord);

            String keyMedia = "";
            String keyRecord = "";
            Record fileMedia = null;
            Record fileRecord = null;
            int i = 0;
            int j = 0;

            for (; i < sizeMedia; i++) {
                if (sStopDiff) {
                    DebugUtil.i(TAG, "diffMediaRecordAndCheckRename mStopDiff for");
                    return;
                }
                fileMedia = mediaData.get(i);
                keyMedia = getKeyForRecord(fileMedia);
                // All recordData has been traversaled, then last mediaData were newly added.
                if (j > sizeRecord - 1) {
                    if (!checkRenameMediaFileFromOtherApp(context, fileMedia)) {
                        insertData.add(fileMedia);
                    }
                } else {
                    for (; j < sizeRecord; j++) {
                        if (sStopDiff) {
                            DebugUtil.i(TAG, "diffMediaRecordAndCheckRename mStopDiff for 2");
                            return;
                        }
                        fileRecord = recordData.get(j);
                        keyRecord = getKeyForRecord(fileRecord);
                        int result = keyMedia.compareToIgnoreCase(keyRecord);
                        if (result == 0) { //same directory and same file name
                            diffMediaProcessEqualsCase(context, fileMedia, fileRecord, insertData, updateData, deleteData);
                            j++;
                            break;
                        } else {
                            if (checkRenameMediaFileFromOtherApp(context, fileMedia)) {
                                checkEncryptBoxDataAndProcessRemainderFiles(context, fileRecord, updateData, deleteData, false);
                                break;
                            } else {
                                if (result > 0) { //check whether need to delete this data from recorder.db
                                    if (j == (sizeRecord - 1)) {
                                        insertData.add(fileMedia);
                                        checkEncryptBoxDataAndProcessRemainderFiles(context, fileRecord, updateData, deleteData, false);
                                        j++;
                                        break;
                                    }
                                    checkEncryptBoxDataAndProcessRemainderFiles(context, fileRecord, updateData, deleteData, false);
                                } else { //in this case, insert media data to recorder.db
                                    insertData.add(fileMedia);
                                    checkEncryptBoxDataAndProcessRemainderFiles(context, fileRecord, updateData, deleteData, false);
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            DebugUtil.d(TAG, "diffMediaRecord, sync db index=" + j);
            // All mediaData has been traversaled, then last galleryData were deleted.
            for (; j < sizeRecord; j++) {
                if (sStopDiff) {
                    DebugUtil.i(TAG, "diffMediaRecordAndCheckRename mStopDiff 3");
                    return;
                }
                checkEncryptBoxDataAndProcessRemainderFiles(context, recordData.get(j), updateData, deleteData, false);
            }

            processDeleteData(context, deleteData);
            processUpdateData(context, updateData);
            processInsertData(context, insertData);
        } else if (recordData.size() > 0) { // only media is empty
            DebugUtil.d(TAG, "only media is empty, and recorder.db is not empty");
            for (Record file : recordData) {
                checkEncryptBoxDataAndProcessRemainderFiles(context, file, updateData, deleteData, false);
            }
            processUpdateData(context, updateData);
            processDeleteData(context, deleteData);
        }
    }

    private void checkEncryptBoxDataAndProcessRemainderFiles(Context context, Record fileRecord, List<Record> updateData, List<Record> deleteData, boolean deleteOnServer) {
        if (fileRecord.fileExist()) {
            return;
        }
        boolean isEncryptFeatureOn = EncryptBoxConstant.ENCRYBOX_FEATURE_ON;
        if (isEncryptFeatureOn) {
            boolean isExistInEncryptBox = EncryptBoxProviderQueryUtil.checkRecordsExistInEncryBox(context, fileRecord.getDisplayName(), fileRecord.getRelativePath(), fileRecord.getMD5());
            if (isExistInEncryptBox) {
                fileRecord.setSyncPrivateStatus(RECORD_PRIVETE_ENCRYPT);
                fileRecord.setSyncType(RecordConstant.SYNC_TYPE_BACKUP);
                fileRecord.setSyncUploadStatus(RecordConstant.SYNC_STATUS_BACKUP_START);
                fileRecord.setDirty(RecordConstant.RECORD_DIRTY_FILE_AND_MEGA);
                if (fileRecord.hasGlobalId()) {
                    updateData.add(fileRecord);
                } else {
                    deleteData.add(fileRecord);
                }
                ConvertDeleteUtil.deleteConvertData(BaseApplication.getAppContext(), fileRecord.getData());
                DebugUtil.i(TAG, "checkEncryptBoxData " + fileRecord.getRelativePath() + fileRecord.getDisplayName() + " exist in Encrybox, add to updateList");
                return;
            }
        }
        if (fileRecord.hasGlobalId()) {
            if (fileRecord.isDeleted()) {
                //do nothing
                DebugUtil.i(TAG, "checkEncryptBoxData, file is deleted " + fileRecord.isDeleted() + ", downloadstatus: " + fileRecord.getSyncDownlodStatus() + ", return");
            } else if (fileRecord.getSyncDownlodStatus() == RecordConstant.SYNC_STATUS_RECOVERY_FILE_SUC) {
                // 曾经下载成功了的，但是现在不在了
                //set wait to download to local;
                fileRecord.setSyncType(SYNC_TYPE_RECOVERY);
                fileRecord.setSyncDownlodStatus(SYNC_STATUS_RECOVERY_MEGADATA_SUC);
                updateData.add(fileRecord);
                DebugUtil.i(TAG, "checkEncryptBoxData,file already downloaded, set wait to download " + fileRecord.getDisplayName());
            } else {
                if (fileRecord.hasFileId() && (fileRecord.getSyncDownlodStatus() <= 0)) {
                    if (deleteOnServer) {
                        fileRecord.setDeleted(true);
                        updateData.add(fileRecord);
                        DebugUtil.i(TAG, "checkEncryptBoxData, delete onServer file2=" + fileRecord.getDisplayName());
                    } else {
                        //set wait to download to local;
                        fileRecord.setSyncType(SYNC_TYPE_RECOVERY);
                        fileRecord.setSyncDownlodStatus(SYNC_STATUS_RECOVERY_MEGADATA_SUC);
                        updateData.add(fileRecord);
                        DebugUtil.i(TAG, "checkEncryptBoxData, set wait to download " + fileRecord.getDisplayName());
                    }
                }
            }
        } else {
            deleteData.add(fileRecord);
            DebugUtil.d(TAG, "checkEncryptBoxData, direct delete file: " + fileRecord.getData());
        }
    }

    private String getKeyForRecord(Record inputRecord) {
        String resultKeyString = null;
        if (BaseUtil.isAndroidQOrLater()) {
            resultKeyString = inputRecord.getConcatRelativePath();
        } else {
            resultKeyString = inputRecord.mData;
        }
        return resultKeyString;
    }

    private static class SqlLimit {
        static final int Q_TYPE = 1;
        static final int P_TYPE = 2;

        private final int mType;

        private String mData;
        private String mRelativePath;
        private String mDisplayName;

        public SqlLimit(String mData) {
            this.mType = P_TYPE;
            this.mData = mData;
        }

        public SqlLimit(String mRelativePath, String mDisplayName) {
            this.mType = Q_TYPE;
            this.mRelativePath = mRelativePath;
            this.mDisplayName = mDisplayName;
        }

        public boolean isQType() {
            return mType == Q_TYPE;
        }

        public String getData() {
            return mData;
        }

        public String getRelativePath() {
            return mRelativePath;
        }

        public String getDisplayName() {
            return mDisplayName;
        }
    }


    public static @Nullable
    Bundle createSqlQueryBundle(
            @Nullable String selection,
            @Nullable String[] selectionArgs,
            @Nullable String sortOrder,
            @Nullable Integer limit,
            @Nullable Integer offset) {

        if ((selection == null) && (selectionArgs == null) && (sortOrder == null)) {
            return null;
        }

        Bundle queryArgs = new Bundle();
        if (selection != null) {
            queryArgs.putString(QUERY_ARG_SQL_SELECTION, selection);
        }
        if (selectionArgs != null) {
            queryArgs.putStringArray(QUERY_ARG_SQL_SELECTION_ARGS, selectionArgs);
        }
        if (sortOrder != null) {
            queryArgs.putString(QUERY_ARG_SQL_SORT_ORDER, sortOrder);
        }
        if (limit != null) {
            queryArgs.putInt(QUERY_ARG_LIMIT, limit);
        }
        if (offset != null) {
            queryArgs.putInt(QUERY_ARG_OFFSET, offset);
        }
        return queryArgs;
    }

    /**
     * 获取支持上传云同步音频文件 mimeType
     *
     * @return
     */
    private String[] getSupportSyncMimeType() {
        if (FunctionOption.IS_SUPPORT_WAV_AND_AAC) {
            return CursorHelper.getsAcceptableAudioTypes();
        }
        return SUPPORT_MIME_TYPE;
    }
}
