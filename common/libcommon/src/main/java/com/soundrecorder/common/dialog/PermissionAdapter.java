/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PermissionAdapter
 * Description:
 * Version: 1.0
 * Date: 2023/10/27
 * Author: W9058795
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9058795 2023/10/27 1.0 create
 */
package com.soundrecorder.common.dialog;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.soundrecorder.common.R;

public class PermissionAdapter extends BaseAdapter {
    private static final int LAYOUT = R.layout.item_permission_content;

    private final Context mContext;
    private final CharSequence[] mPermissionTitles;
    private final CharSequence[] mPermissionSummaries;

    public PermissionAdapter(Context context, CharSequence[] items, CharSequence[] summaries) {
        mContext = context;
        mPermissionTitles = items;
        mPermissionSummaries = summaries;
    }

    @Override
    public int getCount() {
        return (mPermissionTitles == null) ? 0 : mPermissionTitles.length;
    }

    @Override
    public CharSequence getItem(int position) {
        return (mPermissionTitles == null) ? null : mPermissionTitles[position];
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        View contentViewTemp = convertView;
        ViewHolder viewHolder = null;
        if (contentViewTemp == null) {
            contentViewTemp = LayoutInflater.from(mContext).inflate(LAYOUT, parent, false);
            viewHolder = new ViewHolder();
            viewHolder.mItemView = contentViewTemp.findViewById(R.id.text_permission_title);
            viewHolder.mSummaryView = contentViewTemp.findViewById(R.id.text_permission_summary);
            viewHolder.mDivider = contentViewTemp.findViewById(com.support.dialog.R.id.item_divider);
            contentViewTemp.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }

        CharSequence item = getItem(position);
        CharSequence summary = getSummary(position);
        viewHolder.mItemView.setText(item);
        viewHolder.mSummaryView.setText(summary);

        if (viewHolder.mDivider != null) {
            if (getCount() <= 1 || position == getCount() - 1) {
                viewHolder.mDivider.setVisibility(View.GONE);
            } else {
                viewHolder.mDivider.setVisibility(View.VISIBLE);
            }
        }
        return contentViewTemp;
    }

    public CharSequence getSummary(int position) {
        if (mPermissionSummaries == null) {
            return null;
        }
        if (position >= mPermissionSummaries.length) {
            return null;
        }
        return mPermissionSummaries[position];
    }

    private static class ViewHolder {
        TextView mItemView;
        TextView mSummaryView;
        ImageView mDivider;
    }
}
