/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        : CheckOperatorWithPermission
 * * Description : 文件操作（删除/重命名），会先校验所有文件管理权限
 * * Version     : 1.0
 * * Date        : 2022/10/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.fileoperator

import android.app.Activity
import android.net.Uri
import android.os.Handler
import android.os.Looper
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.fileoperator.rename.NameFileDialogUtil
import com.soundrecorder.common.permission.PermissionUtils
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.Future

class CheckOperatorWithPermission(private val activity: Activity?) {

    companion object {
        private const val TAG = "CheckOperatorWithPermission"
    }

    private var mOperateBefore: CheckPermissionBeforeOperate? = null
    private var mOperate: CheckOperate? = null

    private var mExecutorService: ExecutorService? = null
        get() {
            if (field == null) {
                field = Executors.newSingleThreadExecutor()
            }
            return field
        }
    private var mMainHandler: Handler? = null
        get() {
            if (field == null) {
                field = Handler(Looper.getMainLooper())
            }
            return field
        }
    private var mDeleteTask: Future<*>? = null
    private var mRunnable: Runnable? = null

    /**
     * before delete to check AllFileAccessPermission and owner package name
     */
    private fun initOperate() {
        if (mOperate == null) {
            mOperate = CheckOperate()
            mOperate?.setActivity(activity)
        }
        if (mOperateBefore == null) {
            mOperateBefore = CheckPermissionBeforeOperate(activity)
            mOperateBefore?.setCallback(mOperate)
        }
    }

    fun renameUri(
        uri: Uri?,
        newName: String?,
        suffix: String?,
        mimeType: String?,
        nameFileDialogUtil: NameFileDialogUtil
    ) {
        initOperate()
        mOperate?.setOperateStatus(CheckOperate.OPERATE_RENAME)
            ?.setRenameUri(uri, newName, suffix, mimeType, nameFileDialogUtil)
        mOperateBefore?.checkOpsPermission()
    }

    fun deleteRecords(requestCode: Int? = null, deleteRecords: List<Record>?, checkPermission: Boolean = true, deleteFunction: () -> Unit) {
        if (deleteRecords.isNullOrEmpty()) {
            DebugUtil.w(TAG, "deleteRecords, deleteRecords invalid")
            return
        }

        mRunnable = Runnable {
            val ids = mutableListOf<Long>().also {
                for (record in deleteRecords) {
                    it.add(record.id)
                }
            }
            deleteUris(requestCode, ids, checkPermission, deleteFunction)
        }
        mDeleteTask?.cancel(true)
        mDeleteTask = mExecutorService?.submit(mRunnable)
    }

    private fun deleteUris(requestCode: Int?, deleteIds: List<Long>?, checkPermission: Boolean = true, deleteFunction: () -> Unit) {
        if (deleteIds.isNullOrEmpty()) {
            DebugUtil.w(TAG, "deleteIds, deleteRecords invalid")
            return
        }

        if (BaseUtil.isAndroidROrLater) {
            if (PermissionUtils.hasAllFilePermission()) {
                deleteFunction.invoke()
            } else {
                initOperate()
                mOperate?.mOperating = true
                mOperate?.setOperateStatus(CheckOperate.OPERATE_DELETE)?.setDeleteUris(getUriListFromIds(deleteIds), requestCode)
                mMainHandler?.post {
                    mOperateBefore?.checkOpsPermission()
                }
            }
        } else {
            deleteFunction.invoke()
        }
    }

    fun recoverRecords(requestCode: Int? = null, recoverRecords: List<Record>?, checkPermission: Boolean = true, recoverFunction: () -> Unit) {
        if (recoverRecords.isNullOrEmpty()) {
            DebugUtil.w(TAG, "deleteRecords, deleteRecords invalid")
            return
        }

        mRunnable = Runnable {
            val ids = mutableListOf<Long>().also {
                for (record in recoverRecords) {
                    it.add(record.id)
                }
            }
            recoverUris(requestCode, ids, checkPermission, recoverFunction)
        }
        mDeleteTask?.cancel(true)
        mDeleteTask = mExecutorService?.submit(mRunnable)
    }

    private fun recoverUris(requestCode: Int?, recoverIds: List<Long>?, checkPermission: Boolean = true, recoverFunction: () -> Unit) {
        if (recoverIds.isNullOrEmpty()) {
            DebugUtil.w(TAG, "recoverIds, recoverRecords invalid")
            return
        }

        if (BaseUtil.isAndroidROrLater) {
            if (PermissionUtils.hasAllFilePermission()) {
                recoverFunction.invoke()
            } else {
                initOperate()
                mOperate?.mOperating = true
                mOperate?.setOperateStatus(CheckOperate.OPERATE_RECOVER)?.setRecoverUris(getUriListFromIds(recoverIds), requestCode)
                mMainHandler?.post {
                    mOperateBefore?.checkOpsPermission()
                }
            }
        } else {
            recoverFunction.invoke()
        }
    }

    private fun getUriListFromIds(ids: List<Long>): MutableList<Uri> {
        return mutableListOf<Uri>().also {
            for (id in ids) {
                it.add(MediaDBUtils.genUri(id))
            }
        }
    }

    /**
     * 返回需要继续操作的operatorId
     */
    fun needContinueOperator(): Int {
        return if (mOperate?.mOperating == true) {
            mOperate?.getOperateStatus() ?: -1
        } else -1
    }

    fun resetContinueOperator() {
        mOperate?.mOperating = false
    }

    fun getRenameContent(): String = mOperate?.getRenameContent() ?: ""

    fun getOperating(): Boolean = mOperate?.mOperating ?: false

    fun dismissAllFileDialog() {
        mOperateBefore?.dismissAllFileDialog()
    }

    fun release() {
        DebugUtil.e(TAG, "release.. ")
        mOperateBefore?.release()
        mOperateBefore = null
        mOperate = null

        mExecutorService?.shutdown()
        mExecutorService = null
        mMainHandler = null
    }
}