/******************************************************************
 * Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd
 * VENDOR_EDIT
 * File: - MultiFileObserver.java
 * Description:
 * Version: 1.0
 * Date : 2018-12-14
 * Author: <EMAIL>
 **
 * ---------------- Revision History: ---------------------------
 *      <author>        <data>        <version >        <desc>
 *    chentao-FaBen     2018-12-14         1.0
 ********************************************************************/
package com.soundrecorder.common.fileobserve;

import android.os.Environment;
import android.os.FileObserver;
import android.os.Looper;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.constant.RecordModeConstant;
import com.soundrecorder.common.share.ShareUtil;
import com.soundrecorder.common.utils.SettingsAdapter;

import java.io.File;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

public class MultiFileObserver {
    private static final String TAG = "MultiFileObserver";
    private final ConcurrentHashMap<String, FileObserverWrapper> mFileObservers = new ConcurrentHashMap<>();
    private final Set<String> mRemovedFolderPath = new HashSet<>();
    private final ArrayList<OnFileEventListener> mListeners = new ArrayList<>();

    private volatile static MultiFileObserver sInstance = null;

    private final FileObserverMainHandler mMainHandler;

    public static MultiFileObserver getInstance() {
        if (sInstance == null) {
            synchronized (MultiFileObserver.class) {
                if (sInstance == null) {
                    sInstance = new MultiFileObserver();
                }
            }
        }

        return sInstance;
    }

    private MultiFileObserver() {
        mMainHandler = new FileObserverMainHandler(Looper.getMainLooper(), this);
    }

    public void addPath(final String addpath) {
        if (!TextUtils.isEmpty(addpath)) {
            DebugUtil.v(TAG, "addPath= :" + addpath);
            if (mFileObservers.get(addpath) == null) {
                DebugUtil.v(TAG, "addPath name:" + FileUtils.getDisplayNameByPath(addpath));
                File file = new File(addpath);
                if (!file.exists()) {
                    mRemovedFolderPath.add(addpath);
                    return;
                }
                final FileObserver fileObserver = getFileObserver(addpath, file);
                mFileObservers.put(addpath, new FileObserverWrapper(fileObserver));
            }
        }
    }

    @NonNull
    private FileObserver getFileObserver(String addpath, File file) {
        return new FileObserver(file) {
            @Override
            public void onEvent(int event, String path) {
                final String  dir;
                if (addpath.endsWith(File.separator)) {
                    dir = addpath;
                } else {
                    dir = addpath + File.separator;
                }
                String allPath = dir + (TextUtils.isEmpty(path) ? "" : path);
                if (event == FileObserver.DELETE_SELF || event == FileObserver.MOVE_SELF) {
                    mFileObservers.keySet().forEach(key -> {
                        if (key.equals(addpath) || key.startsWith(dir)) {
                            mRemovedFolderPath.add(key);
                            removePath(key);
                            DebugUtil.v(TAG, "onEvent event=" + event + " add removed name:" + key);
                        }
                    });
                }
                //onEvent原始事件在子线程，关注删除流程切换到主线程
                if (event == FileObserver.MOVED_FROM || event == FileObserver.DELETE
                        || event == FileObserver.DELETE_SELF || event == FileObserver.MOVE_SELF) {
                    mMainHandler.dispatchMessage(event, path, allPath);
                } else {
                    MultiFileObserver.this.onEvent(event, path, allPath);
                }
            }
        };
    }

    public void removePath(String path) {
        if (!TextUtils.isEmpty(path)) {
            FileObserverWrapper observerWrapper = mFileObservers.remove(path);
            if (observerWrapper != null) {
                observerWrapper.stopWatching(true);
            }
        }
    }

    public void startWatching() {
        DebugUtil.v(TAG, "startWatching");
        for (FileObserverWrapper observerWrapper : mFileObservers.values()) {
            if (observerWrapper != null) {
                observerWrapper.startWatching(false);
            }
        }
    }

    public void startDeleteFoldWatching() {
        DebugUtil.v(TAG, "startDeleteFoldWatching");
        ArrayList<String> folderPath = new ArrayList<>();
        String[] removedFolderPath = mRemovedFolderPath.toArray(new String[0]);
        for (String fold : removedFolderPath) {
            addPath(fold);
            // 被删除文件夹必须要先stopwatch后，startWatch才生效
            FileObserverWrapper fileObserverWrapper = mFileObservers.get(fold);
            if (fileObserverWrapper != null) {
                DebugUtil.v(TAG, "addPath(fold):" + fold);
                folderPath.add(fold);
                fileObserverWrapper.stopWatching(true);
                fileObserverWrapper.startWatching(true);
            }
        }
        for (String fold : folderPath) {
            mRemovedFolderPath.remove(fold);
        }
    }

    public void stopWatching(boolean force) {
        DebugUtil.v(TAG, "stopWatching");
        for (Map.Entry<String, FileObserverWrapper> entry : mFileObservers.entrySet()) {
            if (entry.getValue() != null) {
                entry.getValue().stopWatching(force);
                if (force || entry.getValue().hasNoWatching()) {
                    mFileObservers.remove(entry.getKey());
                }
            }
        }
        if (force) {
            mListeners.clear();
        }
        clear();
    }

    public void release() {
        stopWatching(false);
        mMainHandler.removeCallbacksAndMessages(null);
        SettingsAdapter.getInstance().release();
    }

    public void clear() {
        mFileObservers.clear();
        mRemovedFolderPath.clear();
    }

    public void restartWatching() {
        startWatching();
    }

    public void addFileEventListener(OnFileEventListener listener) {
        if (listener != null) {
            mListeners.add(listener);
        }
    }

    public void removeFileEventListener(OnFileEventListener listener) {
        if (listener != null) {
            mListeners.remove(listener);
        }
    }

    void onEvent(int event, String path, String allPath) {
        for (OnFileEventListener listeners : mListeners) {
            if (listeners != null) {
                listeners.onFileObserver(event, path, allPath);
            }
        }
    }

    public boolean checkHasFolderRemoved() {
        return mRemovedFolderPath.size() > 0;
    }

    public Set<String> getRemovedFolderPath() {
        return mRemovedFolderPath;
    }

    public boolean checkHasFolderRemoved(String path) {
        return mRemovedFolderPath.contains(path);
    }

    public void addFolders() {
        String storageSdcard = SettingsAdapter.getInstance().getStorageSdcard() + File.separatorChar;
        String storagePhone = SettingsAdapter.getInstance().getStoragePhone() + File.separatorChar;

        String oldRelativePath = null;
        String normalRelativePath = null;
        String callingRelativePath = null;
        String interviewRelativePath = null;
        String meetingRelativePath = null;

        if (BaseUtil.isAndroidQOrLater()) {
            oldRelativePath = Environment.DIRECTORY_MUSIC + File.separator + Constants.RECORDINGS;
            normalRelativePath = RecordModeConstant.STORAGE_RECORD_ABOVE_Q + RecordModeConstant.DIR_STANDARD;
            callingRelativePath = RecordModeConstant.STORAGE_RECORD_ABOVE_Q + RecordModeConstant.DIR_CALL;
            interviewRelativePath = RecordModeConstant.STORAGE_RECORD_ABOVE_Q + RecordModeConstant.DIR_INTERVIEW;
            meetingRelativePath = RecordModeConstant.STORAGE_RECORD_ABOVE_Q + RecordModeConstant.DIR_MEETING;
        } else {
            oldRelativePath = Constants.RECORDINGS;
            normalRelativePath = RecordModeConstant.STORAGE_RECORD + RecordModeConstant.DIR_STANDARD;
            callingRelativePath = RecordModeConstant.STORAGE_RECORD + RecordModeConstant.DIR_CALL;
            interviewRelativePath = RecordModeConstant.STORAGE_RECORD + RecordModeConstant.DIR_INTERVIEW;
            meetingRelativePath = RecordModeConstant.STORAGE_RECORD + RecordModeConstant.DIR_MEETING;
        }

        /*add music folder*/
        if (BaseUtil.isAndroidQOrLater()) {
            addPath(storageSdcard + Environment.DIRECTORY_MUSIC);
            addPath(storagePhone + Environment.DIRECTORY_MUSIC);
        }

        addPath(storageSdcard + oldRelativePath);
        addPath(storagePhone + oldRelativePath);

        addPath(storageSdcard + normalRelativePath);
        addPath(storagePhone + normalRelativePath);

        addPath(storageSdcard + callingRelativePath);
        addPath(storagePhone + callingRelativePath);

        addPath(storageSdcard + interviewRelativePath);
        addPath(storagePhone + interviewRelativePath);

        addPath(storageSdcard + meetingRelativePath);
        addPath(storagePhone + meetingRelativePath);

        if (BaseUtil.isOnePlus()) {
            String opRelativePath;
            if (BaseUtil.isAndroidROrLater()) {
                opRelativePath = RecordModeConstant.OP_STORAGE_RECORD_ABOVE_AND_R;
            } else {
                opRelativePath = RecordModeConstant.OP_STORAGE_RECORD_BELOW_Q;
            }
            addPath(storageSdcard + opRelativePath);
        }
        addPath(storageSdcard + Constants.OPPO_SHARE_RECORDINGS_RELATIVE_ROOT2_PATH);
        addPath(storageSdcard + ShareUtil.INSTANCE.getRelativePathOShare());
        addPath(storageSdcard + Constants.OPPO_SHARE_RECORDINGS_TEXT_ROOT_PATH);
        addPath(storageSdcard + Constants.OPPO_SHARE_RECORDINGS_TEXT_ROOT2_PATH);
        addPath(storageSdcard + ShareUtil.INSTANCE.getRelativePathOShareText());
    }

    public static class FileObserverWrapper {
        private final FileObserver observer;
        private volatile AtomicInteger oNums;

        public FileObserverWrapper(FileObserver observer) {
            this.observer = observer;
            this.oNums = new AtomicInteger(0);
        }

        public void startWatching(boolean force) {
            if (observer != null && (force || oNums.getAndIncrement() <= 0)) {
                observer.startWatching();
            }
        }

        public void stopWatching(boolean force) {
            if (observer != null && (force || oNums.decrementAndGet() <= 0)) {
                observer.stopWatching();
            }
        }

        public boolean hasNoWatching() {
            return oNums.get() <= 0;
        }
    }
}
