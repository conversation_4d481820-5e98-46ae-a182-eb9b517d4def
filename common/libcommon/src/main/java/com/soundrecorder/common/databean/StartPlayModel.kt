/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: StartPlayModel
 * Description:
 * Version: 1.0
 * Date: 2022/11/7
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/11/7 1.0 create
 */

package com.soundrecorder.common.databean

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class StartPlayModel(
    /**媒体库ID*/
    val mediaId: Long = -1L,
    var playPath: String? = null,
    /**是否为非主进程，false意味着是主进程*/
    var isFromOtherApp: Boolean = false,
    /**是否从搜索结果项点击进入播放*/
    var isFromSearch: Boolean = false,
    /**isFromSearch=true，搜索关键词内容*/
    var browseSearchWord: String = "",
    /**进度播放起始时间*/
    var seekToMill: Long? = null,
    /**进入播放页面，默认选中音频/文本，0：音频；1：文本*/
    var selectPosInPlayback: Int = 0,
    /**音频总时长*/
    var duration: Long = 0,
    /**进入播放页，音频是否自动开始播放*/
    var autoPlay: Boolean = false,
    /**是否是从回收站进入*/
    var isRecycle: Boolean = false,
    /**搜索关键词*/
    var searchKeyWord: String = ""
) : Parcelable