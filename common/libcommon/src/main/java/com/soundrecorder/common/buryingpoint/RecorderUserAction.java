/**
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 */
package com.soundrecorder.common.buryingpoint;

import static com.soundrecorder.common.permission.PermissionUtils.isNetWorkGranted;

import android.content.Context;

import com.oplus.statistics.OplusTrack;
import com.soundrecorder.base.utils.BaseUtil;

import java.util.Map;

public class RecorderUserAction {
    /**
     * User action tag
     */
    public static final int USER_ACTION_MAIN_VIEW_TAG = 2000701;
    public static final int USER_ACTION_MARK_TAG = 2000702;
    public static final int USER_ACTION_BROWSEFILE_TAG = 2000703;
    public static final int USER_ACTION_RECORDING_FINISH_TAG = 2000704;
    public static final int USER_ACTION_RECORDER_DURATION = 2000705;
    public static final int USER_ACTION_RECORDER_TYPE = 2000706;
    public static final int USER_ACTION_PLAY_MORE = 2000707;
    public static final int USER_ACTION_TRIM = 2000708;

    public static final int USER_ACTION_CONVERT = 20007010;
    public static final int USER_ACTION_RECORDING_FILE_NAME_INFO_TAG = 20007011;
    public static final int USER_ACTION_FAST_BACK_FAST_FORWARD = 20007012;
    public static final int USER_ACTION_ENTERING_RECORDING = 20007013;
    public static final int USER_ACTION_PLAY_DEVICE_CHANGE = 20007014;
    public static final int USER_ACTION_START_RECORD_TYPE = 20007015;
    public static final int USER_ACTION_SYNC_SWITCH = 20007016;
    public static final int USER_ACTION_RECORD_PLAY_STATUS = 20007017;
    public static final int USER_ACTION_FEEDBACK = 20007018;
    public static final int USER_ACTION_NOTIFICATION = 20007019;
    public static final int USER_ACTION_SETTING = 200070207;

    /**
     * 智能图片标记埋点分组
     */
    public static final int USER_ACTION_SHOW_SMART_MARK = 20007020;
    /**
     * 智能图片标记埋点事件
     */
    public static final int EVENT_COUNT_SHOW_POP = 200070201;
    public static final String KEY_COUNT_SHOW_POP = "count_show_pop";
    public static final int EVENT_COUNT_CLICK_ON_POP = 200070202;
    public static final String KEY_COUNT_CLICK_ON_POP = "count_click_on_pop";
    public static final int EVENT_COUNT_DISMISS_POP_EXCLUDE_USER_ACTION = 200070203;
    public static final String KEY_COUNT_DISMISS_POP_EXCLUDE_USER_ACTION = "count_dismiss_pop_exclude_user_action";
    public static final int EVENT_COUNT_CANCEL_WHEN_ADD_PICTURE_MARK = 200070204;
    public static final String KEY_COUNT_CANCEL_WHEN_ADD_PICTURE_MARK = "count_cancel_when_add_picture_mark";
    public static final int EVENT_COUNT_OK_WHEN_ADD_PICTURE_MARK = 200070205;
    public static final String KEY_COUNT_OK_WHEN_ADD_PICTURE_MARK = "count_ok_when_add_picture_mark";
    public static final int EVENT_COUNT_SHOW_PHOTO_VIEWER = 200070206;
    public static final String KEY_COUNT_SHOW_PHOTO_VIEWER = "count_show_photo_viewer";

    /**
     * 个人信息保护政策
     */
    public static final int USER_ACTION_PERSONAL_PRIVACY_POLICY = 20007022;
    public static final int EVENT_COUNT_CLICK_OK_ON_USER_NOTICE_DIALOG = 22001;
    public static final String KEY_COUNT_CLICK_OK_ON_USER_NOTICE_DIALOG = "count_click_ok_on_user_notice_dialog";
    public static final int EVENT_COUNT_CLICK_OK_ON_USER_NOTICE_BASIC_DIALOG = 22002;
    public static final String KEY_COUNT_CLICK_OK_ON_USER_NOTICE_BASIC_DIALOG = "count_click_ok_on_user_notice_basic_dialog";
    public static final int EVENT_COUNT_CLICK_OK_ON_USER_NOTICE_BASIC_STILL_DIALOG = 22003;
    public static final String KEY_COUNT_CLICK_OK_ON_USER_NOTICE_BASIC_STILL_DIALOG = "count_click_ok_on_user_notice_basic_still_dialog";
    public static final int EVENT_COUNT_CLICK_OK_ON_CONVERT_PERMISSION_DIALOG = 22004;
    public static final String KEY_COUNT_CLICK_OK_ON_CONVERT_PERMISSION_DIALOG = "count_click_ok_on_convert_permission_dialog";
    public static final int EVENT_COUNT_CLICK_OK_ON_CONVERT_PERMISSION_WITHDRAWN_DIALOG = 22005;
    public static final String KEY_COUNT_CLICK_OK_ON_CONVERT_PERMISSION_WITHDRAWN_DIALOG = "count_click_ok_on_convert_permission_withdrawn_dialog";

    public static final String USER_ACTION_DOUBLE_SPEED = "action_double_speed";
    public static final String USER_ACTION_SEARCH_RECORDS = "action_search_records";
    public static final String USER_ACTION_EXPORT = "action_export";
    public static final int USER_ACTION_RECORD_AUDIO_FORMAT = 20007021;
    public static final String USER_ACTION_PLAY_SETTING = "action_play_setting";

    /**
     * 录音分组页
     */
    public static final int USER_ACTION_RECORD_GROUP = 20007023;

    /**
     * User action event id
     */
    public static final int EVENT_RECORDER_START = 200070001;
    public static final int EVENT_BROWSERFILE_DELETE = 200070003;
    public static final int EVENT_BROWSERFILE_RENAME = 200070004;
    public static final int EVENT_BROWSERFILE_ASRINGTONG = 200070005;
    public static final int EVENT_BROWSERFILE_SEND = 200070006;
    public static final int EVENT_BROWSERFILE_FILE_PLAY = 200070007;
    public static final int EVENT_LAUNCH_RECORDER_APP = 200070008;
    public static final int EVENT_MARK_TAG_FROM_RECORDING = 200070009;
    public static final int EVENT_MARK_TAG_FROM_PLAYBACK = 200070010;

    public static final int EVENT_FINISH_RECORDING_DELETE = 200070011;
    public static final int EVENT_FINISH_RECORDING_SAVE = 200070012;
    public static final int EVENT_RENAME_MARK_TAG_FROM_PLAYBACK = 200070013;
    public static final int EVENT_RENAME_MARK_TAG_FROM_RECORDING = 200070014;
    public static final int EVENT_DELETE_MARK_TAG_FROM_PLAYBACK = 200070015;
    public static final int EVENT_DELETE_MARK_TAG_FROM_RECORDING = 200070016;
    public static final int EVENT_SEEK_TO_MARK_TAG_WHEN_PLAYBACK = 200070017;
    public static final int EVENT_RECORDING_TYPE_DURATION = 200070018;
    public static final int EVENT_FAST_FORWARD_CLICK = 200070019;
    public static final int EVENT_FAST_BACK_CLICK = 200070020;

    public static final int EVENT_ENTERING_RECORDING = 200070021;
    public static final int EVENT_FINISH_RECORDING_CANCEL = 200070022;
    public static final int EVENT_PLAY_DEVICE_CHANGE = 200070023;

    public static final int EVENT_NOTIFICATION_LOCK_SCREEN_MARK_BTN_CLICK = 200070024;
    public static final int EVENT_NOTIFICATION_LOCK_SCREEN_PLAY_BTN_CLICK = 200070025;
    public static final int EVENT_NOTIFICATION_LOCK_SCREEN_PUASE_BTN_CLICK = 200070026;
    public static final int EVENT_NOTIFICATION_STATUS_BAR_MARK_BTN_CLICK = 200070027;
    public static final int EVENT_NOTIFICATION_STATUS_BAR_PLAY_BTN_CLICK = 200070028;
    public static final int EVENT_NOTIFICATION_STATUS_BAR_PAUSE_BTN_CLICK = 200070029;

    public static final int EVENT_BROWSERFILE_RECYCLE_DELETE = 200070030;

    public static final int EVENT_BROWSERFILE_RECYCLE_RECOVER = 200070031;

    public static final int EVENT_BROWSERFILE_MOVE_RECORD = 200070032;

    public static final int EVENT_GROUP_NEW_GROUP = 200070033;

    public static final int EVENT_GROUP_RENAME = 200070034;

    public static final int EVENT_GROUP_DELETE = 200070035;

    public static final String EVENT_CLICK_RECORD_GROUP = "event_click_record_group";

    public static final String KEY_GROUP_NAME = "group_name";

    public static final String EVENT_CLICK_CALL_GROUP_BY_CONTACT = "event_click_call_group_by_contact";

    public static final String EVENT_RECORDER_DURATION = "event_record_duration";
    public static final String EVENT_RECORDER_DURATION_NEW = "event_record_duration_new";

    /**
     * 新增埋点
     */
    public static final String FUNCTION_USAGE_INFO = "function_usage_info";
    public static final String ENTRY_LAUNCH = "entry_launch";
    public static final String SETTING_ICON_CLICK = "setting_icon_click";
    public static final String SEARCH_DUR_RECORD = "search_dur_records";
    public static final String SPEAKER_EDIT = "speaker_edit";
    public static final String RECORDING_GROUPING_STATE = "recording_grouping_state";
    public static final String KEY_GROUP_INFO = "group";
    public static final String KEY_GROUP_NAMES = "names";
    public static final String RECORDING_GROUPING_CREATE = "recording_grouping_create";
    public static final String KEY_GROUP_ACTION = "action";
    public static final String KEY_GROUP_TYPE = "type";
    public static final String RECORDING_WHOLE_SHARE = "recording_whole_share";

    public static final String EVENT_RECORDER_TYPE = "event_record_type";
    public static final String EVENT_PLAY_MORE = "event_play_more";
    public static final String EVENT_MARK_UNPLAY = "event_mark_unplay";
    public static final String EVENT_TRIM = "event_trim";
    public static final String EVENT_CONVERT = "event_record_convert";
    public static final String EVENT_START_RECORD_FROM = "event_start_record";
    public static final String EVENT_SYNC_SWITCH = "event_sync_switch";
    public static final String EVENT_RECORD_PLAY_STATUS = "event_record_play_pause";
    public static final String EVENT_DOUBLE_SPEED = "event_double_speed";
    public static final String EVENT_SEARCH_RECORDS = "event_search_records";
    public static final String EVENT_CONVERT_EXPORT_DOC = "event_convert_export_doc";
    public static final String EVENT_CONVERT_EXPORT_NOTICE_INSTALL_WPS = "event_convert_export_install_wps";
    public static final String EVENT_CONVERT_EXPORT_SWITCH_SEGMENTED = "event_convert_export_switch_segmented";
    public static final String EVENT_CONVERT_EXPORT_SWITCH_TIME = "event_convert_export_switch_time";
    public static final String EVENT_CONVERT_EXPORT_SWITCH_SPEAKER = "event_convert_switch_speaker";
    public static final String EVENT_RECORD_AUDIO_FORMAT = "event_choose_audio_format";
    public static final String EVENT_SKIP_MUTE = "event_skip_mute";

    public static final String KEY_RECORD_NAME = "record_name";
    public static final String KEY_RECORD_DURATION = "record_duration";
    public static final String KEY_DUR_MS = "dur_ms";
    public static final String KEY_FILE_ID = "uni_file_id";

    /**
     *单次完整录制音频时间
     * 入口来源 Launcher -> 0  sidebar -> 1 card -> 2 cube_button -> 3
     */
    public static final String KEY_ENTRY_FROM = "entry_from";
    public static final String VALUE_ENTRY_FROM_LAUNCHER = "0";
    public static final String VALUE_ENTRY_FROM_SIDEBAR = "1";
    public static final String VALUE_ENTRY_FROM_CARD = "2";
    public static final String VALUE_ENTRY_FROM_CUBE_BUTTON = "3";

    /**
     * 启动类型 进入录音app -> 1 启动录制音频 -> 2 默认值为 1
     */
    public static final String KEY_ENTRY_TYPE = "entry_type";

    /**
     * 落地页前后台 前台 -> 1 后台 -> 2
     * 启动类型 进入录音app -> 1 启动录制音频 -> 2 默认值为 1
     */
    public static final String KEY_LANDING_TYPE = "landing_type";

    /**
     * 录音模式
     */
    public static final String KEY_REC_TYPE = "rec_type";

    /**
     * 返回类型 保存 -> 1 取消 -> 2 杀掉进程 -> 0
     */
    public static final String KEY_RETURN = "return_type";
    public static final int VALUE_RETURN_TYPE_SAVE = 1;
    public static final int VALUE_RETURN_TYPE_CANCEL = 2;
    public static final int VALUE_RETURN_TYPE_FINISH = 0;

    /**
     *设置页按钮名称：
     * 录音云同步 录音文件格式 录音模式 智慧语音 图片标记推荐
     * 关于录音 收集个人信息明示清单 隐私 帮助与反馈
     */
    public static final String KEY_NAME = "name";
    public static final String VALUE_NAME_RECORD_CLOUD = "name_record_cloud";
    public static final String VALUE_NAME_RECORD_TYPE = "name_record_type";
    public static final String VALUE_NAME_RECORD_MODE = "name_record_mode";
    public static final String VALUE_NAME_SMART_RECORD = "name_smart_record";
    public static final String VALUE_NAME_PICTURE_TAG = "name_picture_tag";
    public static final String VALUE_NAME_ABOUT_RECORD = "name_about_record";
    public static final String VALUE_NAME_PERSONAL_INFO = "name_personal_info";
    public static final String VALUE_NAME_PRIVACY = "name_privacy";
    public static final String VALUE_NAME_HELP = "name_help";

    /**
     * 按钮选项
     * 录音模式： 标准 会议 采访
     * 录音文件格式 mp3 aac wav
     */
    public static final String KEY_OPTION = "option";
    public static final String VALUE_OPTION_RECORD_TYPE_DEFAULT = "0";
    public static final String VALUE_OPTION_RECORD_TYPE_STANDARD = "1";
    public static final String VALUE_OPTION_RECORD_TYPE_MEETING = "2";
    public static final String VALUE_OPTION_RECORD_TYPE_INTERVIEW = "3";
    public static final String VALUE_OPTION_RECORD_FORMAT_MP3 = "mp3";
    public static final String VALUE_OPTION_RECORD_FORMAT_AAC = "aac";
    public static final String VALUE_OPTION_RECORD_FORMAT_WAV = "wav";


    /**
     * 搜索
     * 搜索场景： 首页搜索 -> 0 详情页搜索 -> 1
     * playCount: 点击快捷播放次数（仅首页上报）
     * infoCount: 点击播放详情页次数（仅首页上报）
     */
    public static final String KEY_SEARCH_SCENE = "search_scene";
    public static final String VALUE_SEARCH_FRONT_PAGE = "0";
    public static final String VALUE_SEARCH_INFO_PAGE = "1";

    public static final String KEY_PLAY_COUNT = "play_count";
    public static final String KEY_INFO_COUNT  = "info_count";

    /**
     * 来源：录音转文本 -> 0
     */
    public static final String KEY_EDIT_FROM = "from";
    public static final String VALUE_EDIT_FROM = "0";
    public static final String VALUE_REALTIME_FROM = "1";

    /**
     * 是否应用到全部讲话人 是 -> 1 否 -> 0
     */
    public static final String KEY_IF_APPLIED_ALL = "if_applied_all";
    public static final String VALUE_APPLY_ALL_YES = "1";
    public static final String VALUE_APPLY_ALL_NO = "0";
    public static final String KEY_EDIT_COUNT = "edit_count";

    public static final String KEY_RECORD_TYPE = "record_type";
    public static final String KEY_RECORD_MODE = "recordmode";
    public static final String KEY_IS_EDITED = "isEdited";

    public static final String VALUE_RECORD_TYPE_STANDARD = "0";
    public static final String VALUE_RECORD_TYPE_MEETING = "1";
    public static final String VALUE_RECORD_TYPE_INTERVIEW = "2";
    public static final String VALUE_RECORD_TYPE_CALL = "3";

    public static final String KEY_FAST_BACK = "fast_back";
    public static final String VALUE_FAST_BACK = "0";
    public static final String KEY_FAST_FORWARD = "fast_forward";
    public static final String VALUE_FAST_FORWARD = "0";

    public static final String KEY_FROM_NOTIFICATION = "from_notification";
    public static final String VALUE_FROM_NOTIFICATION = "0";

    public static final String KEY_FROM_STATUS_BAR = "from_status_bar";
    public static final String VALUE_FROM_STATUS_BAR = "0";

    public static final String KEY_PLAY_TRIM = "play_more_trim";
    public static final String VALUE_PLAY_TRIM = "0";
    public static final String KEY_MORE_SHARE = "play_more_share";
    public static final String VALUE_MORE_SHARE = "0";
    public static final String KEY_MORE_RENAME = "play_more_rename";
    public static final String VALUE_MORE_RENAME = "0";
    public static final String KEY_PLAY_MORE_RENAME_SUCCESS = "play_more_rename_success";
    public static final String KEY_PLAY_MORE_RENAME_BTN_CLICK = "play_more_rename_btn_click";
    public static final String VALUE_RENAME_DIALOG_NAVIGATION_BTN_SAVE = "0";
    public static final String VALUE_RENAME_DIALOG_NAVIGATION_BTN_CANCEL = "1";
    public static final String VALUE_RENAME_DIALOG_PLAYBACK_BTN_SAVE = "2";
    public static final String VALUE_RENAME_DIALOG_PLAYBACK_BTN_CANCEL = "3";
    public static final String KEY_MORE_DELETE = "play_more_delete";

    public static final String KEY_RECYCLE_DELETE = "play_recycle_delete";
    public static final String KEY_RECYCLE_RECOVER = "play_recycle_recover";

    public static final String KEY_PLAY_MORE_MOVE = "play_more_move";

    public static final String VALUE_MORE_DELETE = "0";
    public static final String KEY_PLAY_MORE_DELETE_SUCCESS = "play_more_delete_success";

    public static final String KEY_PLAY_RECYCLE_DELETE_SUCCESS = "play_recycle_delete_success";

    public static final String KEY_PLAY_RECYCLE_RECOVER_SUCCESS = "play_recycle_recover_success";

    public static final String KEY_PLAY_MORE_DETAIL = "play_more_detail";
    public static final String VALUE_PLAY_MORE_DETAIL = "0";

    public static final String KEY_MARK_UNPLAY = "mark_unplay";
    public static final String VALUE_MARK_UNPLAY = "0";

    public static final String KEY_TRIM_CANCLE = "trim_cancle";
    public static final String VALUE_TRIM_CANCLE = "0";
    public static final String VALUE_TRIM_CANCLE_EXIT = "1";
    public static final String VALUE_TRIM_CANCLE_CONTINUE = "2";

    public static final String KEY_TRIM_SAVE = "trim_save";
    public static final String VALUE_TRIM_SAVE_RETAIN = "0";
    public static final String VALUE_TRIM_SAVE_NO_RETAIN = "1";

    public static final String KEY_TRIM_EDIT_NAME = "trim_edit_name";
    public static final String VALUE_TRIM_EDIT_NAME = "0";

    public static final String KEY_TRIM_DELETE = "trim_delete";
    public static final String VALUE_TRIM_DELETE = "0";

    public static final String KEY_TRIM_EXTRACT = "trim_extract";
    public static final String VALUE_TRIM_EXTRACT = "0";

    public static final String KEY_TRIM_SET_START = "trim_set_start";
    public static final String VALUE_TRIM_SET_START = "0";
    public static final String KEY_TRIM_SET_END = "trim_set_end";
    public static final String VALUE_TRIM_SET_END = "0";
    public static final String KEY_TRIM_DRAG_WAVE = "trim_drag_wave";
    public static final String VALUE_TRIM_DRAG_WAVE = "0";
    public static final String KEY_TRIM_DRAG_PREVIEW = "trim_drag_preview";
    public static final String VALUE_TRIM_DRAG_PREVIEW = "0";

    public static final String KEY_CONVERT_START = "convert_start";
    public static final String KEY_CONVERT_STOP = "convert_stop";
    public static final String KEY_CONVERT_CANCEL = "convert_cancel";
    public static final String KEY_EXPORT_COPY = "export_copy";
    public static final String KEY_EXPORT_TXT = "export_txt";
    public static final String KEY_EXPORT_CANCEL = "export_cancel";

    /**
     * convert rename speaker
     */
    public static final String KEY_SPEAKER_CLICK_TIPS = "speaker_click_speaker_tip";
    public static final String KEY_SPEAKER_RENAME_BY_HISTORY = "speaker_rename_by_history";
    public static final String KEY_SPEAKER_RENAME_ALL = "speaker_rename_all";
    public static final String KEY_SPEAKER_RENAME_CURRENT = "speaker_rename_current";

    /**
     * share txt setting dialog
     */
    public static final String KEY_SHARE_TXT_SETTING_SWITCH = "switch";
    public static final String VALUE_SHARE_TXT_SETTING_SWITCH = "0";
    public static final String VALUE_SHARE_TXT_SETTING_SWITCH_REPORT = "1";

    /**
     * 转文本分人按钮点击
     */
    public static final String KEY_SHOW_SPEAKERS = "show_speakers";
    public static final String VALUE_SHOW_SPEAKERS_SHOWING = "1";
    public static final String VALUE_SHOW_SPEAKERS_UNSHOWING = "0";

    public static final String VALUE_CONVERT_START = "0";
    public static final String VALUE_CONVERT_STOP = "0";
    public static final String VALUE_CONVERT_CANCEL = "0";
    public static final String VALUE_EXPORT_COPY = "0";
    public static final String VALUE_EXPORT_TXT = "0";
    public static final String VALUE_EXPORT_CANCEL = "0";
    public static final String KEY_EXPORT_CLICK_EXPORT = "export_click_export";
    public static final String VALUE_EXPORT_CLICK_EXPORT_COMPLETE = "0";
    public static final String KEY_EXPORT_TO_NOTE = "export_send_to_note";
    public static final String VALUE_EXPORT_TO_NOTE = "0";
    /**
     * convert rename speaker
     */
    public static final String VALUE_SPEAKER_CLICK_TIPS = "0";
    public static final String VALUE_SPEAKER_RENAME_BY_HISTORY = "0";
    public static final String VALUE_SPEAKER_RENAME_ALL = "0";
    public static final String VALUE_SPEAKER_RENAME_CURRENT = "0";

    public static final String KEY_RECORD_PLAY_DEVICE_BROWSE = "record_play_device_browse";
    public static final String KEY_RECORD_PLAY_DEVICE = "record_play_device";
    public static final String VALUE_PLAY_SPEAKER_ON = "1";
    public static final String VALUE_PLAY_SPEAKER_OFF = "2";

    public static final String KEY_START_RECORD_TYPE = "start_type";
    public static final String VALUE_START_RECORD_FROM_LAUNCHER = "0";
    public static final String VALUE_START_RECORD_FROM_MESSAGE = "1";
    public static final String VALUE_START_RECORD_FROM_SIDEBAR = "2";

    public static final String VALUE_START_RECORD_FROM_CUBE_BUTTON = "3";

    public static final String KEY_SYNC_SWITCH = "switch";
    public static final String VALUE_SYNC_SWITCH_ON = "0";
    public static final String VALUE_SYNC_SWITCH_OFF = "1";

    public static final String KEY_RECORD_PLAY_STATUS = "play_status";
    public static final String VALUE_RECORD_PLAY_BROWSEFILE = "0";
    public static final String VALUE_RECORD_PLAY_PAUSE_BROWSEFILE = "1";
    public static final String VALUE_RECORD_PLAY_PLAYBACK = "2";
    public static final String VALUE_RECORD_PLAY_PAUSE_PLAYBACK = "3";

    public static final String KEY_MULTIPLE_SPEED = "speed";
    public static final String VALUE_MULTIPLE_SPEED_HALF = "3";
    public static final String VALUE_MULTIPLE_SPEED_NORMAL = "0";
    public static final String VALUE_MULTIPLE_SPEED_ONE_DOT_FIVE = "1";
    public static final String VALUE_MULTIPLE_SPEED_TWICE = "2";

    public static final String KEY_PLAY_FROM = "from";
    public static final String VALUE_PLAY_FROM_BROWSEFILE = "0";
    public static final String VALUE_PLAY_FROM_PLAYBACK = "1";
    public static final String KEY_PLAY_DURATION = "duration";
    public static final String KEY_PLAY_MODE = "recordMode";

    public static final String KEY_SEARCH_RECORDS = "search";
    public static final String VALUE_SEARCH_CLICK = "0";
    public static final String VALUE_SEARCH_CANCEL_CLICK = "1";
    public static final String VALUE_SEARCH_PLAY_BROWSEFILE = "2";
    public static final String VALUE_SEARCH_PLAY_PLAYBACK = "3";

    /**
     * pictrue mark
     */
    public static final String KEY_RECORDING_TAKE_PICTURE = "recording_take_picture";
    public static final String KEY_RECORDING_PICTURE_OK = "recording_picture_ok";
    public static final String KEY_RECORDING_PICTURE_CANCEL = "recording_picture_cancel";
    public static final String KEY_PLAY_TAKE_PICTURE = "play_take_picture";
    public static final String KEY_PLAY_PICTURE_CANCEL = "play_picture_cancel";
    public static final String KEY_PLAY_TAKE_PICTURE_NUM = "play_take_picture_num";
    public static final String KEY_PLAY_ALBUM_PICTURE_NUM = "play_album_picture_num";
    public static final String KEY_PLAY_ALBUM_PICTURE_CANCEL = "play_album_picture_cancel";
    public static final String KEY_PLAY_ALBUM_PICTURE_OK = "play_album_picture_ok";
    public static final String KEY_PLAY_PICTURE_DIALOG_SHOW_NOT_ONCLICK_NUM = "play_picture_dialog_show_not_onclick_num";
    public static final String KEY_PICTURE_ONCLICK_NUM = "picture_onclick_num";

    public static final String KEY_RECORD_AUDIO_FORMAT = "audio_format";
    public static final String VALUE_RECORD_AUDIO_FORMAT_WAV = "wav";
    public static final String VALUE_RECORD_AUDIO_FORMAT_AAC = "aac";
    public static final String VALUE_RECORD_AUDIO_FORMAT_MP3 = "mp3";

    public static final String DEFAULT_VALUE = "0";

    /**
     * play setting
     */
    public static final String KEY_SKIP_MUTE = "skip_mute";
    public static final String VALUE_SKIP_MUTE_OPEN = "0";
    public static final String VALUE_SKIP_MUTE_CLOSE = "1";

    /**
     * 进入转文本搜索界面
     */
    public static final String EVENT_CONVERT_SEARCH = "convert_search";
    public static final String IN_SEARCH = "in_search";
    public static final String COUNT = "count";
    public static final String KEY_CONTENT_SEARCH = "content_search";
    public static final String KEY_CANCEL_CONTENT_SEARCH = "cancel_content_search";
    public static final String KEY_CONTENT_SEARCH_POS_CHANGE = "content_search_pos_change";
    public static final String VALUE_POS_CHANGE_PREVIOUS = "0";
    public static final String VALUE_POS_CHANGE_NEXT = "1";

    /**
     * 负一屏卡片添加/移除埋点
     */
    public static final String GROUP_BREENO_CARD = "group_breeno_card";
    public static final String EVENT_SMALL_CARD = "event_small_card";
    public static final String ADD_CARD = "add_card";

    /**
     * 点击关键词标签
     */
    public static final String EVENT_KEY_WORD = "click_key_word_chip";

    /**
     * cloud start
     */
    public static final String USER_ACTION_CLOUD = "could_event";
    public static final String EVENT_CLOUD_TIPS_CARD = "cloud_tips_card";
    public static final String KEY_CLOUD_TIPS_POP_NUM = "cloud_tips_pop_num";
    public static final String KEY_CLOUD_TIPS_CLICK_OPEN_NUM = "cloud_tips_click_open_num";
    public static final String KEY_CLOUD_TIPS_CLICK_IGNORE_NUM = "cloud_tips_click_ignore_num";
    public static final String KEY_CLOUD_TIPS_CLICK_VIEW_DATA_NUM = "cloud_tips_click_view_data_num";
    public static final String KEY_CLOUD_TIPS_UPGRADE_SPACE_POP_NUM = "cloud_tips_upgrade_space_pop_num";
    public static final String KEY_CLOUD_TIPS_UPGRADE_SPACE_CLICK_NUM = "cloud_tips_upgrade_space_click_num";

    /**
     * 云同步顶部tips服务端配置
     */
    public static final String KEY_CLOUD_OPERATION_ID = "config_id";
    public static final String KEY_CLOUD_OPERATION_CONTENT = "content";
    public static final String KEY_CLOUD_OPERATION_BUTTON_TYPE = "button_type";
    public static final String KEY_CLOUD_OPERATION_BUTTON_CONTENT = "button_content";
    public static final String EVENT_CLOUD_EXPOSURE = "cloud_operation_exposure";
    public static final String EVENT_CLOUD_CLICK = "cloud_operation_click";
    public static final String CLICK_EVENT = "click_event";
    public static final String EXPOSURE_EVENT = "exposure_event";

    /**
     * 状态胶囊/流体云相关埋点定义
     */
    public static final String USER_ACTION_SEEDLING_CARD = "seedling_card_event";
    public static final String EVENT_SEEDLING_CARD_FLUID = "seedling_card_fluid";
    public static final String KEY_SEEDLING_CARD_FLUID_RECORD_BTN_CLICK = "record_btn_click";
    public static final String KEY_SEEDLING_CARD_FLUID_MARK_BTN_CLICK = "mark_btn_click";

    /**
     * question 问卷相关埋点定义
     */
    public static final String USER_ACTION_QUESTIONAIR = "question_event";
    public static final String EVENT_QUESTION_TIPS_CARD = "question_tip_card";
    public static final String EVENT_QUESTION_TIPS_CARD_NOT_SHOW = "question_tip_card_not_show";

    public static final String KEY_QUESTION_TIPS_POP_NUM = "question_tip_pop_num";
    public static final String KEY_QUESTION_TIPS_CLICK_OPEN_NUM = "question_tip_click_open_num";
    public static final String KEY_QUESTION_TIPS_CLICK_IGNORE_NUM = "question_tip_click_ignore_num";

    public static final String KEY_QUESTION_TIPS_NOT_SHOW_PERROID_NOT_MATCH = "question_tip_notshow_period_not_match";
    public static final String KEY_QUESTION_TIPS_NOT_SHOW_CONTENT_EMPTY = "question_tip_notshow_content_empty";
    public static final String KEY_QUESTION_TIPS_NOT_SHOW_REPOSITORY_FAILED = "question_tip_notshow_respository_failed";
    public static final String KEY_QUESTION_TIPS_NOT_SHOW_REPOSITORY_VALUE_EMPTY = "question_tip_notshow_respository_value_empty";

    public static final String VALUE_REASON_TRUE = "1";

    public static final String KEY_MINI_CANCEL_RECORD = "mini_cancel_record";
    public static final String VALUE_MINI_CANCEL_RECORD_CLOSE = "0";
    public static final String VALUE_MINI_CANCEL_RECORD_BTN_DELETE = "1";
    public static final String VALUE_MINI_CANCEL_RECORD_BTN_CANCEL = "2";

    public static final String KEY_PLAY_MORE_EDIT = "play_more_edit";
    public static final String VALUE_PLAY_MORE_EDIT = "0";
    public static final String KEY_PLAY_MORE_SETTING = "play_more_setting";
    public static final String VALUE_PLAY_MORE_SETTING = "0";
    public static final String KEY_CLICK_BROWSE_RECORD_FILE = "click_browse_record_file";
    public static final String VALUE_NORMAL_RECORD_FILE = "0";
    public static final String VALUE_SELECT_RECORD_FILE = "1";

    public static final String EVENT_RECORD_GROUP_PANEL = "event_record_group_panel";
    public static final String KEY_CLICK_RECORD_GROUP_PANEL = "click_record_group_panel";
    public static final String VALUE_PANEL_FROM_TITLE = "0";
    public static final String VALUE_PANEL_FROM_TRIANGLE = "1";
    public static final String EVENT_START_RECORD_AUDIO = "event_start_record_audio";

    public static final String EVENT_DIRECT_RECORD_BUTTON_CLICK = "event_click_deirect_record_button";
    public static final String EVENT_DIRECT_RECORD_BUTTON_STATE = "state";
    public static final String EVENT_DIRECT_RECORD_FILE_ID = "fileId";
    public static final String EVENT_DIRECT_RECORD_FROM = "from";
    public static final String VALUE_DIRECT_FROM_RECORDER_PAGE = "0";

    public static final String KEY_CLICK_RECORD_STATE = "click_record_state";
    public static final String VALUE_CONTINUE_RECORD = "0";
    public static final String VALUE_PAUSE_RECORD = "1";

    public static final String VALUE_DIRECT_RECORD_BUTTON_OPEN = "1";

    public static final String VALUE_DIRECT_RECORD_BUTTON_CLOSE = "0";
    public static final String KEY_CLICK_SAVE_RECORD = "click_save_record";
    public static final String VALUE_SAVE_RECORD = "0";
    public static final String VALUE_SAVE_RECORD_DIALOG_CANCEL = "1";
    public static final String VALUE_SAVE_RECORD_DIALOG_SAVE = "2";
    public static final String VALUE_SAVE_RECORD_DIALOG_SAVE_CHANGE_NAME = "3";
    public static final String VALUE_SAVE_RECORD_SMALL_CARD = "4";
    public static final String VALUE_SAVE_RECORD_MINI_CARD = "5";
    public static final String VALUE_SAVE_RECORD_SEEDLING_CARD = "6";
    public static final String VALUE_SAVE_RECORD_NOTIFICATION = "7";
    public static final String VALUE_RENAME_RECORD_DIALOG_CANCEL = "RenameRecordDialogCancel";
    public static final String VALUE_RENAME_RECORD_DIALOG_SAVE = "RenameRecordDialogSave";
    public static final String KEY_CLICK_CANCEL_RECORD = "click_cancel_record";
    public static final String VALUE_CLOSE_RECORD = "0";
    public static final String VALUE_EXIT_DELETE_RECORD = "1";
    public static final String VALUE_CANCEL_RECORD = "2";
    public static final String KEY_CONVERT_TEXT = "convert_text";
    public static final String KEY_LOOK_TEXT = "look_text";
    public static final String KEY_CLICK_SHARE_TEXT = "click_share_text";
    public static final String KEY_CLICK_PLAY_TEXT_MARK = "click_play_text_mark";
    public static final String EVENT_SMALL_CARD_START_RECORD = "event_small_card_start_record";
    public static final String KEY_CLICK_SMALL_CARD_START_RECORD = "click_small_card_start_record";
    public static final String KEY_CLICK_RECORD_TEXT_MARK = "click_record_text_mark";
    public static final String VALUE_CLICK_RECORD_TEXT_MARK = "0";
    public static final String KEY_PLAY_MORE_SHARE_RECORD_TXT = "play_more_share_record_txt";
    public static final String VALUE_SHARE_RECORDING = "0";
    public static final String VALUE_SHARE_TEXTING = "1";
    public static final String KEY_CLICK_RECORD_TEXT_MARK_IN_SMALL_CARD = "click_record_text_mark_in_small_card";
    public static final String VALUE_CLICK_RECORD_TEXT_MARK_IN_SMALL_CARD = "0";
    public static final String EVENT_MINI_APP_CLICK_LOOK = "event_mini_app_click_look";
    public static final String KEY_CLICK_MINI_CARD_LOOK = "click_mini_card_look";
    public static final String VALUE_CLICK_MINI_CARD_LOOK = "0";
    public static final String KEY_CLICK_RECORD_TEXT_MARK_IN_MINI_CARD = "click_record_text_mark_in_mini_card";
    public static final String VALUE_CLICK_RECORD_TEXT_MARK_IN_MINI_CARD = "0";
    public static final String KEY_CLICK_RECORD_STATE_IN_MINI_CARD = "click_record_state_in_mini_card";
    public static final String VALUE_CONTINUE_RECORD_MINI_CARD = "0";
    public static final String VALUE_PAUSE_RECORD_MINI_CARD = "1";
    public static final String KEY_CLICK_RECORD_STATE_IN_DRAGONFLY_CARD = "click_record_state_in_dragonfly_card";
    public static final String VALUE_CONTINUE_RECORD_DRAGONFLY_CARD = "0";
    public static final String VALUE_PAUSE_RECORD_DRAGONFLY_CARD = "1";
    public static final String KEY_CLICK_RECORD_STATE_IN_SMALL_CARD = "click_record_state_in_small_card";
    public static final String VALUE_CONTINUE_RECORD_SMALL_CARD = "0";
    public static final String VALUE_PAUSE_RECORD_SMALL_CARD = "1";
    public static final String EVENT_CONVERT_DURATION = "event_convert_duration";
    public static final String KEY_PLAY_SWITCH_TAB = "play_switch_tab";
    public static final String VALUE_PLAY_SWITCH_TAB_AUDIO = "0";
    public static final String VALUE_PLAY_SWITCH_TAB_TEXT = "1";
    public static final String KEY_ENTER_SETTING_OPEN = "enter_setting_open";
    public static final String VALUE_ENTER_SETTING_OPEN = "0";
    public static final String KEY_CONVERT_CANCEL_DIALOG = "convert_cancel_dialog";
    public static final String KEY_CONVERT_CONTINUE_DIALOG = "convert_continue_dialog";
    public static final String KEY_EXPORT_DOC = "export_doc";
    public static final String KEY_PLAY_MARK_LIST_BUTTON = "play_mark_list_button";
    public static final String VALUE_PLAY_MARK_LIST_UNSHOWING = "0";
    public static final String VALUE_PLAY_MARK_LIST_SHOWING = "1";
    public static final String EVENT_PLAY_SETTING = "event_play_setting";
    public static final String KEY_PLAY_SETTING = "play_setting";
    public static final String EVENT_RESTORE_ALL = "event_restore_all";
    public static final String KEY_RESTORE_ALL = "restore_all";
    public static final String KEY_TRIM_MENU_SAVE = "trim_menu_save";
    public static final String KEY_TRIM_DIALOG_SAVE_CANCEL = "trim_dialog_save_cancel";
    public static final String VALUE_TRIM_DIALOG_SAVE = "0";
    public static final String VALUE_TRIM_DIALOG_CANCEL = "1";
    public static final String KEY_TRIM_PLAY_PAUSE = "trim_play_pause";
    public static final String VALUE_TRIM_PLAY = "0";
    public static final String VALUE_TRIM_PAUSE = "1";
    public static final String KEY_NOT_INSTALL_WPS = "not_install_wps";
    public static final String KEY_EXPORT_DOC_NUM = "export_doc_num";
    public static final String KEY_THROUGH_CALL_RECORDING = "through_call_recording";
    public static final String KEY_LAUNCH_RECORDER_APP = "launch_recorder_app";
    public static final String KEY_CONVERT_SUCCESS_DURATION = "convert_success_duration";
    public static final String KEY_CONVERT_FILE_DURATION = "convert_file_duration";
    public static final String KEY_CONVERT_CANCEL_DURATION = "convert_cancel_duration";
    public static final String KEY_CONVERT_FAIL_DURATION = "convert_fail_duration";
    public static final String KEY_NOTIFICATION_MARK_LOCK = "notification_mark_lock";
    public static final String VALUE_NOTIFICATION_MARK_LOCK_NORMAL_RECORDING = "1";
    public static final String VALUE_NOTIFICATION_MARK_LOCK_SECOND_RECORDING = "2";
    public static final String VALUE_NOTIFICATION_MARK_LOCK_NORMAL_PLAYBACK = "3";
    public static final String VALUE_NOTIFICATION_MARK_LOCK_SECOND_PLAYBACK = "4";
    public static final String KEY_NOTIFICATION_MARK_UNLOCK = "notification_mark_unlock";
    public static final String VALUE_NOTIFICATION_MARK_UNLOCK_NORMAL_RECORDING = "1";
    public static final String VALUE_NOTIFICATION_MARK_UNLOCK_SECOND_RECORDING = "2";
    public static final String VALUE_NOTIFICATION_MARK_UNLOCK_NORMAL_PLAYBACK = "3";
    public static final String VALUE_NOTIFICATION_MARK_UNLOCK_SECOND_PLAYBACK = "4";
    public static final String KEY_NOTIFICATION_PLAY_LOCK = "notification_play_lock";
    public static final String VALUE_NOTIFICATION_PLAY_LOCK_NORMAL_RECORDING = "1";
    public static final String VALUE_NOTIFICATION_PLAY_LOCK_SECOND_RECORDING = "2";
    public static final String VALUE_NOTIFICATION_PLAY_LOCK_NORMAL_PLAYBACK = "3";
    public static final String VALUE_NOTIFICATION_PLAY_LOCK_SECOND_PLAYBACK = "4";
    public static final String KEY_NOTIFICATION_PAUSE_LOCK = "notification_pause_lock";
    public static final String VALUE_NOTIFICATION_PAUSE_LOCK_NORMAL_RECORDING = "1";
    public static final String VALUE_NOTIFICATION_PAUSE_LOCK_SECOND_RECORDING = "2";
    public static final String VALUE_NOTIFICATION_PAUSE_LOCK_NORMAL_PLAYBACK = "3";
    public static final String VALUE_NOTIFICATION_PAUSE_LOCK_SECOND_PLAYBACK = "4";
    public static final String KEY_NOTIFICATION_PLAY_UNLOCK = "notification_play_unlock";
    public static final String VALUE_NOTIFICATION_PLAY_UNLOCK_NORMAL_RECORDING = "1";
    public static final String VALUE_NOTIFICATION_PLAY_UNLOCK_SECOND_RECORDING = "2";
    public static final String VALUE_NOTIFICATION_PLAY_UNLOCK_NORMAL_PLAYBACK = "3";
    public static final String VALUE_NOTIFICATION_PLAY_UNLOCK_SECOND_PLAYBACK = "4";
    public static final String KEY_NOTIFICATION_PAUSE_UNLOCK = "notification_pause_unlock";
    public static final String VALUE_NOTIFICATION_PAUSE_UNLOCK_NORMAL_RECORDING = "1";
    public static final String VALUE_NOTIFICATION_PAUSE_UNLOCK_SECOND_RECORDING = "2";
    public static final String VALUE_NOTIFICATION_PAUSE_UNLOCK_NORMAL_PLAYBACK = "3";
    public static final String VALUE_NOTIFICATION_PAUSE_UNLOCK_SECOND_PLAYBACK = "4";
    public static final String KEY_LAUNCH_APP_SMALL_CARD = "launch_app_small_card";
    public static final String VALUE_LAUNCH_APP_SMALL_CARD = "0";
    public static final String VALUE_LAUNCH_APP_SMALL_CARD_RECORD_SAVE = "1";
    public static final String KEY_FROM_BREENO = "from_breeno";
    public static final String KEY_FROM = "from";
    public static final String VALUE_SET_RINGTONE_FROM_EDIT = "0";
    public static final String VALUE_SET_RINGTONE_FROM_MORE = "1";
    /*拖拽成功eventId*/
    public static final String EVENT_ID_FILE_DRAG_SUCCESS = "record_file_drag_out_success";
    /*拖拽成功事件的参数，文件数量*/
    public static final String KEY_FILE_DRAG_SUCCESS_COUNT = "file_count";

    /*实时字幕*/
    public static final String EVENT_REAL_TIME_SUBTITLE = "real_time_subtitle";
    public static final String KEY_UNI_FILE_ID = "uni_file_id";
    public static final String KEY_STATE = "state";
    public static final String KEY_F_MS = "f_ms";
    public static final String KEY_RETURN_1 = "return";
    public static final String KEY_FAIL_REASON = "fail_reason";
    /*无网络*/
    public static final int FAIL_REASON_NO_NETWORK = 0;
    /*服务异常*/
    public static final int FAIL_REASON_SERVICE_EXCEPTION = 1;
    /*其他异常*/
    public static final int FAIL_REASON_OTHER = 2;

    public static void addCommonUserAction(Context context, int logTag, int eventId, Map eventMap) {
        if (isNetWorkGranted(context)) {
            OplusTrack.onCommon(context, String.valueOf(logTag), String.valueOf(eventId), eventMap);
        }
    }

    public static void addCommonUserAction(Context context, int logTag, int eventId, Map eventMap, boolean uploadNow) {
        if (isNetWorkGranted(context)) {
            OplusTrack.onCommon(context, String.valueOf(logTag), String.valueOf(eventId), eventMap);
        }
    }

    public static void addNewCommonUserAction(Context context, int logTag, String eventId, Map eventMap, boolean uploadNow) {
        if (isNetWorkGranted(context) || BaseUtil.isEXP()) {
            OplusTrack.onCommon(context, String.valueOf(logTag), eventId, eventMap);
        }
    }

    public static void addNewCommonUserAction(Context context, String logTag, String eventId, Map eventMap, boolean uploadNow) {
        if (isNetWorkGranted(context)) {
            OplusTrack.onCommon(context, logTag, eventId, eventMap);
        }
    }
}
