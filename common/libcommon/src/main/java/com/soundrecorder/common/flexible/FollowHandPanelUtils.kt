/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: FollowHandPanelUtils
 Description: https://odocs.myoas.com/docs/dPkpKOLxRvixYEqO?mobileMsgShowStyle=1&pcMsgShowStyle=1&mobileMsgShowStyle=1&pcMsgShowStyle=1
 Version: 1.0
 Date: 2023/03/21
 Author: ********(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 22023/03/21 1.0 create
 */

package com.soundrecorder.common.flexible

import android.annotation.SuppressLint
import android.app.Activity
import android.app.ActivityOptions
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import androidx.fragment.app.Fragment
import com.oplus.flexiblewindow.FlexibleWindowManager
import com.oplus.os.OplusBuild
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.R
import com.soundrecorder.common.task.ActivityTaskUtils
import com.soundrecorder.common.utils.ViewUtils.doOnLayoutChange

@Suppress("TooGenericExceptionCaught")
object FollowHandPanelUtils {

    private const val TAG = "FollowHandPanelUtils"
    private const val SUPPORT_FOLLOW_PANEL_ADDON_SDK_VERSION = 29
    private const val SUPPORT_FOLLOW_PANEL_ADDON_SDK_SUB_VERSION = 29

    @JvmStatic
    fun startActivityForResult(fragment: Fragment, intent: Intent, requestCode: Int) {
        if (isAddOnSupportFollowPanel()) {
            kotlin.runCatching {
                val options = buildActivityOptions()
                val bundle =
                    FlexibleWindowManager.getInstance().setExtraBundle(options, buildBundle())
                fragment.startActivityForResult(intent, requestCode, bundle)
            }.onFailure {
                DebugUtil.d("$TAG startActivityForResult", it.message)
                fragment.startActivityForResult(intent, requestCode)
            }
        } else {
            fragment.startActivityForResult(intent, requestCode)
        }
    }

    @JvmStatic
    fun startActivity(context: Context, intent: Intent) {
        if (isAddOnSupportFollowPanel()) {
            kotlin.runCatching {
                val options = buildActivityOptions()
                val exBundle = buildBundle()
                val bundle = FlexibleWindowManager.getInstance().setExtraBundle(options, exBundle)
                context.startActivity(intent, bundle)
            }.onFailure {
                DebugUtil.d("$TAG startActivity", it.message)
                context.startActivity(intent)
            }
        } else {
            context.startActivity(intent)
        }
    }

    @JvmStatic
    fun isFlexibleActivitySuitable(configuration: Configuration): Boolean {
        kotlin.runCatching {
            val isFlexibleActivitySuitable =
                FlexibleWindowManager.isFlexibleActivitySuitable(configuration)
            DebugUtil.d(TAG, "isFlexibleActivitySuitable == $isFlexibleActivitySuitable")
            return isFlexibleActivitySuitable
        }.onFailure {
            DebugUtil.d(TAG, "isFlexibleActivitySuitable error == ${it.message}")
        }
        return false
    }

    @JvmStatic
    fun checkActivityClickOutOfBounds(activity: Activity, rootView: View?) {
        if (!isAddOnSupportFollowPanel()) {
            return
        }
        rootView?.doOnLayoutChange { _, _, _ ->
            val decorView = activity.window.decorView
            val toolBar = rootView.findViewById<View>(com.support.panel.R.id.toolbar)
            val appBarLayout = rootView.findViewById<View>(com.support.preference.R.id.appbar_layout)
            if (isFlexibleActivitySuitable(activity.resources.configuration)) {
                dealClickOutOfBounds(activity)
                // 跟手面板，改变面板颜色（处理暗色下，跟手面板背景色 和 背景色融在了一起）
                decorView.setBackgroundResource(R.color.common_follow_panel_background)
                toolBar?.setBackgroundResource(R.color.common_follow_panel_background)
                appBarLayout?.setBackgroundResource(R.color.common_follow_panel_background)
            } else {
                decorView.setOnTouchListener(null)
                // 非面板
                decorView.setBackgroundResource(com.soundrecorder.base.R.color.common_background_color)
                toolBar?.setBackgroundResource(com.soundrecorder.base.R.color.common_background_color)
                appBarLayout?.setBackgroundResource(com.soundrecorder.base.R.color.common_background_color)
            }
        }
    }

    /**
     * 跟手弹窗区域外点击处理
     */
    @SuppressLint("ClickableViewAccessibility")
    @JvmStatic
    fun dealClickOutOfBounds(activity: Activity) {
        activity.setFinishOnTouchOutside(false)
        val decorView = activity.window.decorView
        decorView.setOnTouchListener { _, event ->
            if ((event.action == MotionEvent.ACTION_UP) && isOutOfBounds(activity, event)) {
                backToBrowseFileActivity(activity)
            }
            false
        }
    }

    /**
     * addOn 是否支持跟手面板
     * addOn版本号必须大于29.29
     * apk往下发布兼容
     */
    @JvmStatic
    fun isAddOnSupportFollowPanel(): Boolean {
        kotlin.runCatching {
            if (!BaseUtil.isAndroidROrLater) {
                DebugUtil.i(TAG, "not support follow  below R")
                return false
            }
            val sdkVersion = OplusBuild.VERSION.SDK_VERSION
            val sdkSubVersion = OplusBuild.VERSION.SDK_SUB_VERSION
            DebugUtil.d(TAG, "SDK_VERSION = $sdkVersion, SDK_SUB_VERSION = $sdkSubVersion")
            if (((sdkVersion == SUPPORT_FOLLOW_PANEL_ADDON_SDK_VERSION)
                        && (sdkSubVersion >= SUPPORT_FOLLOW_PANEL_ADDON_SDK_SUB_VERSION))
                || (sdkVersion > SUPPORT_FOLLOW_PANEL_ADDON_SDK_VERSION)) {
                return true
            }
        }.onFailure {
            return false
        }
        return false
    }

    /**
     * 返回首页
     */
    @JvmStatic
    fun backToBrowseFileActivity(activity: Activity) {
        ActivityTaskUtils.backToBrowse(activity.taskId)
    }

    /**
     * 判断是否点击超出屏幕
     * 系统提供
     */
    @JvmStatic
    private fun isOutOfBounds(activity: Activity, event: MotionEvent): Boolean {
        val x = event.x.toInt()
        val y = event.y.toInt()
        val slop = ViewConfiguration.get(activity).scaledWindowTouchSlop
        val decorView: View = activity.window.decorView
        return (x < -slop || y < -slop
                || x > decorView.width + slop
                || y > decorView.height + slop)
    }

    @JvmStatic
    private fun buildActivityOptions(): ActivityOptions {
        /**
         * 可以在ActivityOptions中声明启动的bounds(bounds需要满足!Rect.isEmpty())
         * ActivityOptions options = ActivityOptions.makeBasic()
         *  options.setLaunchBounds(rect);
         */
        val options = ActivityOptions.makeBasic()
        return options
    }

    @JvmStatic
    private fun buildBundle(): Bundle {
        val exBundle = Bundle()
        /**
         * 以页面浮窗启动Activity  true/false
         */
        exBundle.putBoolean(FlexibleWindowManager.KEY_FLEXIBLE_START_ACTIVITY, true)
        /**
         * 继承启动。再次标准启动Activity——Context#startActivity时，activity是否跟随上一个面板显示。
         * 值：true代表跟随，false代表不跟随。
         */
        exBundle.putBoolean(FlexibleWindowManager.KEY_FLEXIBLE_ACTIVITY_DESCENDANT, false)
        /**
         * 跟手面板的启动位置
         * FLEXIBLE_ACTIVITY_POSITION_LEFT  跟手启动至左侧
         * FLEXIBLE_ACTIVITY_POSITION_RIGHT   跟手启动至右侧
         */
        var position = FlexibleWindowManager.FLEXIBLE_ACTIVITY_POSITION_LEFT
        if (BaseApplication.sIsRTLanguage) {
            position = FlexibleWindowManager.FLEXIBLE_ACTIVITY_POSITION_RIGHT
        }
        exBundle.putInt(
            FlexibleWindowManager.KEY_FLEXIBLE_ACTIVITY_POSITION,
            position
        )
        return exBundle
    }
}