/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderActivity.java
 Description:
 Version: 1.0
 Date: 2009-12-15
 Author: zhanghr
 -----------Revision History-----------
 <author> <date> <version> <desc>
 zhanghr 2009-12-15 create
 */

package com.soundrecorder.common.permission

import android.app.Activity
import android.content.DialogInterface.BUTTON_POSITIVE
import android.graphics.Typeface
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.text.style.TypefaceSpan
import android.view.Gravity
import android.widget.LinearLayout
import android.widget.ListAdapter
import androidx.appcompat.app.AlertDialog
import androidx.core.view.doOnPreDraw
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.R
import com.soundrecorder.common.dialog.PermissionAdapter
import com.soundrecorder.common.permission.PermissionUtils.POST_NOTIFICATIONS
import com.soundrecorder.common.permission.PermissionUtils.READ_EXTERNAL_STORAGE
import com.soundrecorder.common.permission.PermissionUtils.READ_MEDIA_AUDIO
import com.soundrecorder.common.permission.PermissionUtils.READ_MEDIA_IMAGES
import com.soundrecorder.common.permission.PermissionUtils.RECORD_AUDIO
import com.soundrecorder.common.permission.PermissionUtils.WRITE_EXTERNAL_STORAGE
import com.soundrecorder.common.utils.ViewUtils

object PermissionDialogUtils {
    const val TYPE_PERMISSION_ALL_FILE_ACCESS = 0
    const val TYPE_PERMISSION_OTHER = 1
    const val TYPE_PERMISSION_POST_NOTIFICATION = 2

    /**
     * DIALOG type
     */
    const val TYPE_DIALOG_DEFAULT = 0 // 内屏默认居中显示
    const val TYPE_DIALOG_TINY = 1  // 外屏底部显示，POSITIVE按钮主题色

    /**
     * 数组长度 2
     */
    private const val SIZE_TWO = 2

    /**
     * @param dialogStyleType 弹窗样式
     * @see TYPE_DIALOG_DEFAULT 内屏权限弹窗，居中显示
     * or
     * @see TYPE_DIALOG_TINY 外屏权限弹窗，底部显示
     */
    @JvmStatic
    fun showPermissionsDialog(
        activity: Activity,
        listener: PermissionDialogListener,
        permissions: Array<String>,
        dialogStyleType: Int = TYPE_DIALOG_DEFAULT
    ): AlertDialog? {
        val newPermissions = permissions.toCollection(ArrayList())
        if (newPermissions.size > 1 && permissions.contains(POST_NOTIFICATIONS) && PermissionUtils.hasShowNotificationPermissionDialog(activity)) {
            newPermissions.remove(POST_NOTIFICATIONS)
        }
        return if (newPermissions.size == 1) {
            when (newPermissions[0]) {
                READ_MEDIA_AUDIO -> showReadAudioPermissionDialog(activity, listener, dialogStyleType, newPermissions)
                READ_MEDIA_IMAGES -> showReadImagePermissionDialog(activity, listener, dialogStyleType, newPermissions)
                RECORD_AUDIO -> showRecordAudioPermissionDialog(activity, listener, dialogStyleType, newPermissions)
                POST_NOTIFICATIONS -> showPostNotificationPermissionDialog(activity, listener, dialogStyleType, newPermissions)
                else -> showReadStoragePermissionDialog(activity, listener, dialogStyleType, newPermissions)
            }
        } else if (checkOnlyReadAndWritePermission(newPermissions)) {
            showReadStoragePermissionDialog(activity, listener, dialogStyleType, newPermissions)
        } else if (newPermissions.size > 1) {
            showMultiPermissionsDialog(newPermissions, activity, listener, dialogStyleType)
        } else {
            null
        }
    }

    /**
     * 检测权限数组仅仅只包含READ_EXTERNAL_STORAGE和WRITE_EXTERNAL_STORAGE
     */
    @JvmStatic
    private fun checkOnlyReadAndWritePermission(newPermissions: MutableList<String>): Boolean {
        return newPermissions.size == SIZE_TWO && newPermissions.contains(READ_EXTERNAL_STORAGE) && newPermissions.contains(WRITE_EXTERNAL_STORAGE)
    }

    @JvmStatic
    fun showPermissionAllFileAccessDialog(
        activity: Activity,
        listener: PermissionDialogListener
    ): AlertDialog {
        return showPermissionDialog(
            activity,
            R.string.all_file_access_dialog_tile_v2,
            activity.getString(R.string.all_file_access_dialog_decription_v3),
            listener,
            TYPE_PERMISSION_ALL_FILE_ACCESS,
            TYPE_DIALOG_DEFAULT
        )
    }

    @JvmStatic
    fun showReadStoragePermissionDialog(
        activity: Activity,
        listener: PermissionDialogListener,
        dialogStyleType: Int = TYPE_DIALOG_DEFAULT,
        permissions: ArrayList<String>
    ): AlertDialog {
        return showPermissionDialog(
            activity,
            R.string.storage_permission_open,
            activity.getString(R.string.storage_permission_describe_new_v2),
            listener,
            TYPE_PERMISSION_OTHER,
            dialogStyleType,
            permissions = permissions
        )
    }

    @JvmStatic
    fun showReadAudioPermissionDialog(
        activity: Activity,
        listener: PermissionDialogListener,
        dialogStyleType: Int,
        permissions: ArrayList<String>
    ): AlertDialog {
        return showPermissionDialog(
            activity,
            R.string.permission_open_read_audio_dialog_title_v2,
            activity.getString(R.string.permission_open_read_audio_dialog_desc_v2),
            listener,
            TYPE_PERMISSION_OTHER,
            dialogStyleType,
            permissions = permissions
        )
    }

    @JvmStatic
    fun showReadImagePermissionDialog(
        activity: Activity,
        listener: PermissionDialogListener,
        dialogStyleType: Int,
        permissions: ArrayList<String>
    ): AlertDialog {
        return showPermissionDialog(
            activity,
            R.string.permission_open_read_image_title,
            activity.getString(R.string.permission_open_read_image_desc),
            listener,
            TYPE_PERMISSION_OTHER,
            dialogStyleType,
            permissions = permissions
        )
    }

    @JvmStatic
    fun showRecordAudioPermissionDialog(
        activity: Activity,
        listener: PermissionDialogListener,
        dialogStyleType: Int,
        permissions: ArrayList<String>
    ): AlertDialog {
        return showPermissionDialog(
            activity,
            R.string.microphone_permission_open,
            activity.getString(R.string.recorder_permission_describe_new_v2),
            listener,
            TYPE_PERMISSION_OTHER,
            dialogStyleType,
            permissions = permissions
        )
    }

    @JvmStatic
    fun showPostNotificationPermissionDialog(
        activity: Activity,
        listener: PermissionDialogListener,
        dialogStyleType: Int,
        permissions: ArrayList<String>
    ): AlertDialog {
        return showPermissionDialog(
            activity,
            R.string.permission_open_notification_title,
            activity.getString(R.string.permission_open_notification_content),
            listener,
            TYPE_PERMISSION_POST_NOTIFICATION,
            dialogStyleType,
            permissions = permissions
        )
    }

    @JvmStatic
    fun showMultiPermissionsDialog(
        permissions: ArrayList<String>,
        activity: Activity,
        listener: PermissionDialogListener,
        dialogStyleType: Int
    ): AlertDialog {
        val map = mutableMapOf<String, String>()
        //麦克风
        if (permissions.contains(RECORD_AUDIO)) {
            map[activity.getString(R.string.microphone)] = activity.getString(R.string.permission_recording_use_record)
        }
        //T及以上音乐和其他音频
        if (permissions.contains(READ_MEDIA_AUDIO)) {
            map[activity.getString(R.string.permission_read_audio_v2)] = activity.getString(R.string.permission_save_audio_file_new)
        }
        //T以下读取、写入权限
        if (permissions.contains(READ_EXTERNAL_STORAGE)) {
            map[activity.getString(R.string.storage_space)] = activity.getString(R.string.read_write_recorder_content_describe)
        }
        //通知权限
        if (permissions.contains(POST_NOTIFICATIONS)) {
            map[activity.getString(R.string.permission_notification_title)] = activity.getString(R.string.permission_open_notification_new)
        }
        var permissionsAdapter: PermissionAdapter? = null
        val message: CharSequence
        if (dialogStyleType == TYPE_DIALOG_TINY) {
            message = showTinyDialog(map, activity)
        } else {
            message = activity.getString(R.string.recorder_need_open_permission_hint)
            permissionsAdapter = PermissionAdapter(
                activity, map.keys.toTypedArray(),
                map.values.toTypedArray()
            )
        }
        val dialog = showPermissionDialog(
            activity, R.string.permission_open,
            message, listener, TYPE_PERMISSION_OTHER, dialogStyleType, permissionsAdapter, true, permissions = permissions
        )
        dialog.window?.decorView?.doOnPreDraw {
            if (permissions.contains(POST_NOTIFICATIONS)) {
                //通知受阻弹窗只显示一次
                PermissionUtils.setHasShowNotificationPermissionDialog(activity)
            }
        }
        return dialog
    }

    @JvmStatic
    private fun showTinyDialog(
        map: Map<String, String>,
        activity: Activity
    ): CharSequence {
        val spannableString = SpannableStringBuilder()
        spannableString.append(activity.getString(R.string.recorder_need_open_permission_hint))
        spannableString.appendLine()
        map.forEach { (title, desc) ->
            spannableString.appendLine()
            spannableString.append(title)
            spannableString.appendLine()
            spannableString.append(desc)
        }
        val text = spannableString.toString()
        val typeface = Typeface.create("sans-serif-medium", Typeface.NORMAL)
        map.forEach { (title, desc) ->
            var start = text.indexOf(title)
            var end = start + title.length
            spannableString.setSpan(
                TypefaceSpan(typeface),
                start,
                end,
                Spanned.SPAN_INCLUSIVE_EXCLUSIVE
            )
            start = text.indexOf(desc)
            end = start + desc.length
            spannableString.setSpan(
                ForegroundColorSpan(COUIContextUtil.getAttrColor(activity, com.support.appcompat.R.attr.couiColorSecondNeutral)),
                start,
                end,
                Spanned.SPAN_INCLUSIVE_EXCLUSIVE
            )
        }
        return spannableString
    }

    @JvmStatic
    private fun showPermissionDialog(
        activity: Activity,
        title: Int,
        message: CharSequence,
        listener: PermissionDialogListener,
        permissionType: Int,
        dialogStyleType: Int = TYPE_DIALOG_DEFAULT,
        adapter: ListAdapter? = null,
        isMultiShow: Boolean? = false,
        permissions: ArrayList<String>? = null
    ): AlertDialog {
        val builder = when (dialogStyleType) {
            TYPE_DIALOG_DEFAULT -> COUIAlertDialogBuilder(activity).setAdapter(adapter, null)
            TYPE_DIALOG_TINY -> COUIAlertDialogBuilder(activity, com.support.dialog.R.style.COUIAlertDialog_Bottom_Tiny)
            else -> COUIAlertDialogBuilder(activity)
        }
        val negativeTxt = if (isMultiShow == true && dialogStyleType == TYPE_DIALOG_DEFAULT) {
            R.string.runtime_permission_runtime_cancel
        } else {
            R.string.cancel
        }
        return builder.setTitle(title)
            .setBlurBackgroundDrawable(true)
            .setMessage(message)
            .setPositiveButton(R.string.app_name_settings) { _, _ ->
                listener.onClick(permissionType, true, permissions)
            }.setNegativeButton(negativeTxt) { _, _ ->
                listener.onClick(permissionType, false)
            }.setOnCancelListener {
                DebugUtil.d("PermissionDialogUtils", "Permission dialog on cancel")
                listener.onClick(permissionType, false) // back键响应取消按钮点击事件
            }.show().apply {
                setCancelable(true)
                setCanceledOnTouchOutside(false)
                listener.dialogPermissionType(permissionType)
                findViewById<LinearLayout>(com.support.dialog.R.id.main_layout)?.setHorizontalGravity(Gravity.START)
                if (dialogStyleType == TYPE_DIALOG_TINY) {
                    // 设置按钮再外屏下更改为主题色背景，白色文字
                    (getButton(BUTTON_POSITIVE) as? COUIButton)?.let {
                        it.setDrawableColor(
                            COUIContextUtil.getAttrColor(
                                context,
                                com.support.appcompat.R.attr.couiColorPrimary
                            )
                        )
                        it.setTextColor(
                            COUIContextUtil.getColor(
                                context,
                                com.support.appcompat.R.color.coui_btn_default_text_normal_color
                            )
                        )
                        it.invalidate()
                    }
                }
                ViewUtils.updateWindowLayoutParams(window)
            }
    }

    interface PermissionDialogListener {
        fun onClick(alertType: Int, isOk: Boolean, permissions: ArrayList<String>? = null)
        fun onBackPress(alertType: Int) {}

        /**
         * 当前dialog展示的权限类型
         */
        fun dialogPermissionType(dialogPermissionType: Int) {}
    }
}