/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.soundrecorder.common.buryingpoint;

import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.provider.MediaStore;

import java.util.HashMap;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.common.db.CursorHelper;


import com.soundrecorder.common.constant.RecordModeConstant;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.utils.ConvertDbUtil;


public class BuryingPointUtil {
    public static final String TAG = "BuryingPointUtil";
    /*统计用户录音文件数量(所有文件、标准录音、会议录音、通话录音、采访录音)*/
    public static final String EVENT_USER_FILE_COUNT = "user_audio_file_count";
    public static final String KEY_EVENT_FILE_COUNT_STANDARD = "standard_file_count";
    public static final String KEY_EVENT_FILE_COUNT_MEETING = "meeting_file_count";
    public static final String KEY_EVENT_FILE_COUNT_INTERVIEW = "interview_file_count";
    public static final String KEY_EVENT_FILE_COUNT_CALL = "call_file_count";
    public static final String KEY_EVENT_FILE_COUNT_ALL = "all_file_count";
    public static final String KEY_EVENT_FILE_COUNT_CONVERT = "convert_file_count";
    private static final String[] FILE_KEYS = {MediaStore.Audio.Media.DISPLAY_NAME, MediaStore.Audio.Media.DURATION, MediaStore.Audio.Media.RELATIVE_PATH};
    private static final String DEFAULT_RECORDER_TYPE = "-1";

    /**
     * 此方法包含2部分埋点（音频数量埋点、单条音频信息埋点）
     * @param context
     */
    public static void addRecorderFileInfoEvent(Context context) {
        String whereClause = CursorHelper.getAllRecordContainCallWhereClause(context);
        String[] selectionArgs = CursorHelper.getsAcceptableAudioTypes();
        ContentResolver resolver = context.getContentResolver();
        Cursor cursor = null;
        int standardCount = 0;
        int meetingCount = 0;
        int interviewCount = 0;
        int callCount = 0;
        int allCount = 0;
        int convertCount = 0;
        try {
            convertCount = ConvertDbUtil.queryConvertSuccessCount();
            cursor = resolver.query(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, FILE_KEYS, whereClause, selectionArgs, null);
            if (cursor != null) {
                allCount = cursor.getCount();
            }
            if ((cursor != null) && cursor.moveToFirst()) {
                do {
                    int nameIndex = cursor.getColumnIndex(FILE_KEYS[0]);
                    String name = "";
                    if (nameIndex >= 0) {
                        name = cursor.getString(nameIndex);
                    }
                    int durationIndex = cursor.getColumnIndex(FILE_KEYS[1]);
                    long duration = 0;
                    if (durationIndex >= 0) {
                        duration = cursor.getLong(durationIndex);
                    }
                    int reletivePathIndex = cursor.getColumnIndex(FILE_KEYS[2]);
                    String relativePath = "";
                    if (reletivePathIndex >= 0) {
                        relativePath = cursor.getString(reletivePathIndex);
                    }
                    String recorderType = DEFAULT_RECORDER_TYPE;
                    if (relativePath.endsWith(RecordModeConstant.INSTANCE.getDIR_STANDARD_END())
                            || relativePath.endsWith(RecordModeConstant.INSTANCE.getDIR_OPPO_SHARE_END())) {
                        recorderType = RecorderUserAction.VALUE_RECORD_TYPE_STANDARD;
                        standardCount += 1;
                    } else if (relativePath.endsWith(RecordModeConstant.INSTANCE.getDIR_MEETING_END())) {
                        recorderType = RecorderUserAction.VALUE_RECORD_TYPE_MEETING;
                        meetingCount += 1;
                    } else if (relativePath.endsWith(RecordModeConstant.INSTANCE.getDIR_INTERVIEW_END())) {
                        recorderType = RecorderUserAction.VALUE_RECORD_TYPE_INTERVIEW;
                        interviewCount += 1;
                    } else if (relativePath.endsWith(RecordModeConstant.INSTANCE.getDIR_CALL_END())) {
                        recorderType = RecorderUserAction.VALUE_RECORD_TYPE_CALL;
                        callCount += 1;
                    }
                    HashMap<String, String> eventInfo = new HashMap<>();
                    eventInfo.put(RecorderUserAction.KEY_RECORD_NAME, name);
                    eventInfo.put(RecorderUserAction.KEY_RECORD_DURATION, String.valueOf(duration));
                    eventInfo.put(RecorderUserAction.KEY_RECORD_TYPE, recorderType);
                    RecorderUserAction.addCommonUserAction(BaseApplication.getAppContext(),
                            RecorderUserAction.USER_ACTION_RECORDING_FILE_NAME_INFO_TAG,
                            RecorderUserAction.EVENT_RECORDING_TYPE_DURATION,
                            eventInfo, false);
                } while (cursor.moveToNext());
            }
        } catch (Throwable e) {
            DebugUtil.e(TAG, "query exception", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            addUserAudioFileCount(allCount, standardCount, meetingCount, interviewCount, callCount, convertCount);
        }
    }

    /**
     * 统计用户音频文件总数量
     * @param standardCount 标准录音数量
     * @param meetingCount 会议录音数量
     * @param interviewCount 采访录音数量
     * @param callCount 通话录音数量
     */
    public static void addUserAudioFileCount(int allCount, int standardCount, int meetingCount, int interviewCount, int callCount, int convertCount) {
        HashMap<String, String> eventInfo = new HashMap<>();
        eventInfo.put(KEY_EVENT_FILE_COUNT_STANDARD, String.valueOf(standardCount));
        eventInfo.put(KEY_EVENT_FILE_COUNT_MEETING, String.valueOf(meetingCount));
        eventInfo.put(KEY_EVENT_FILE_COUNT_INTERVIEW, String.valueOf(interviewCount));
        eventInfo.put(KEY_EVENT_FILE_COUNT_CALL, String.valueOf(callCount));
        eventInfo.put(KEY_EVENT_FILE_COUNT_ALL, String.valueOf(allCount));
        eventInfo.put(KEY_EVENT_FILE_COUNT_CONVERT, String.valueOf(convertCount));
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_MAIN_VIEW_TAG,
                EVENT_USER_FILE_COUNT,
                eventInfo, false);
        DebugUtil.i(TAG," addUserAudioFileCount ");
    }

    public static void addFromNotification() {
        HashMap<String, String> eventInfo = new HashMap<>();
        eventInfo.put(RecorderUserAction.KEY_FROM_NOTIFICATION, RecorderUserAction.VALUE_FROM_NOTIFICATION);
        RecorderUserAction.addCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_ENTERING_RECORDING,
                RecorderUserAction.EVENT_ENTERING_RECORDING,
                eventInfo, false);

        DebugUtil.i(TAG, "addFromNotification");
    }

    public static void addFromStatusBar() {
        HashMap<String, String> eventInfo = new HashMap<>();
        eventInfo.put(RecorderUserAction.KEY_FROM_STATUS_BAR, RecorderUserAction.VALUE_FROM_STATUS_BAR);
        RecorderUserAction.addCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_ENTERING_RECORDING,
                RecorderUserAction.EVENT_ENTERING_RECORDING,
                eventInfo, false);

        DebugUtil.i(TAG, "addFromStatusBar");
    }


}