package com.soundrecorder.common.db

import android.content.ContentProviderOperation
import android.content.ContentValues
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.util.Pair
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.databean.KeyWord

/**
 * 关键词的DB工具类
 */
object KeyWordDbUtils {

    private const val TAG = "KeyWordDbUtils"

    /**
     * 关键词的表名
     */
    const val TABLE_KEY_WORD_NAME = "key_word"

    /**
     * 数据库版本
     */
    const val DATABASE_VERSION_KEY_WORD = 9

    const val PROVIDER_KEY_WORD_TYPE = "vnd.android.cursor.dir/key_word"

    /**
     * 主键id
     */
    const val ID = "id"

    /**
     * 对应的录音id
     */
    const val RECORD_ID = "record_id"

    /**
     * 文件路径
     */
    const val MEDIA_PATH = "media_path"

    /**
     * 关键词
     */
    const val NAME = "name"

    /**
     * 关键词打分的权重
     */
    const val TFIDF_VALUE = "tfidfValue"

    private val keyWordUri = DatabaseConstant.getContentUri(TABLE_KEY_WORD_NAME)


    /**
     * 创建key_word表
     */
    @JvmStatic
    fun createKeyWordTable(db: SQLiteDatabase) {
        DebugUtil.i(TAG, "crateKeyWordTable")
        db.execSQL(
            "CREATE TABLE IF NOT EXISTS " + TABLE_KEY_WORD_NAME + " ("
                    + ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
                    + RECORD_ID + " INTEGER,"
                    + NAME + " TEXT,"
                    + TFIDF_VALUE + " FLOAT "
                    + ");"
        )
    }

    /**
     * 升级key_word表
     * @param db
     * @param fromVersion 老版本
     * @param toVersion 新版本
     */
    @JvmStatic
    fun upgradeKeyWordTable(db: SQLiteDatabase, fromVersion: Int, toVersion: Int) {
        DebugUtil.e(TAG, "upgradeKeyWordTable from:$fromVersion to:$toVersion")
        if (fromVersion >= toVersion) { // 升级时，from < to ,如果大于等于，则说明传入的值错误
            DebugUtil.e(TAG, "upgradeKeyWordTable version error")
            return
        }
        for (version in (fromVersion + 1..toVersion)) {
            when (version) { //只处理 version >= 9 的情况
                DATABASE_VERSION_KEY_WORD -> {
                    createKeyWordTable(db)
                }
            }
        }
    }

    /**
     * 降级数据库表key_word
     * @param db
     * @param fromVersion
     * @param toVersion
     */
    @JvmStatic
    fun downgradeKeyWordTable(db: SQLiteDatabase, fromVersion: Int, toVersion: Int) {
        DebugUtil.e(TAG, "downgradeKeyWordTable from:$fromVersion to:$toVersion")
        if (fromVersion <= toVersion) { // 降级时，from > to，如果小于等于，则说明版本错误
            DebugUtil.e(TAG, "downgradeKeyWordTable version error")
            return
        }

        for (version in (fromVersion downTo toVersion + 1)) {
            when (version) { //只处理 version >= 9 的情况
                DATABASE_VERSION_KEY_WORD -> {
                    dropKeyWordTable(db)
                }
            }
        }
    }

    /**
     * 删除 key_word表
     */
    @JvmStatic
    fun dropKeyWordTable(db: SQLiteDatabase) {
        DebugUtil.e(TAG, "dropKeyWordTable")
        db.execSQL("DROP TABLE IF EXISTS $TABLE_KEY_WORD_NAME;")
    }

    private fun getContentResolver() = BaseApplication.getAppContext().contentResolver

    /**
     * 添加关键词
     * @param keyWord 关键词
     * @param recordId 录音的id
     */
    fun addKeyWord(keyWord: KeyWord, recordId: Long = 0) {
        try {
            keyWord.recordId = recordId
            val contentValues = getContentValues(keyWord)
            val contentResolver = getContentResolver()
            contentResolver.insert(keyWordUri, contentValues)
            DebugUtil.d(TAG, "addKeyWord recordId:$recordId $keyWord")
        } catch (e: Exception) {
            DebugUtil.e(TAG, "addKeyWord error", e)
        }
    }

    /**
     * 获取ContentValues
     */
    private fun getContentValues(keyWord: KeyWord): ContentValues {
        val contentValues = ContentValues().apply {
            put(RECORD_ID, keyWord.recordId)
            put(NAME, keyWord.name)
            put(TFIDF_VALUE, keyWord.tfidfvalue)
        }
        return contentValues
    }

    /**
     * 添加关键词列表
     * @param keyWords 关键词列表
     * @param recordId 录音id
     */
    fun addKeyWords(keyWords: List<KeyWord>, recordId: Long = 0) {
        try {
            val delCount = deleteKeyWords(recordId) // 先删除再插入，防止出现脏数据
            DebugUtil.d(TAG, "addKeyWords delete record $recordId key words, delete count $delCount")

            val operations = ArrayList<ContentProviderOperation>()
            keyWords.forEach { keyWord ->
                keyWord.recordId = recordId
                val contentValues = getContentValues(keyWord)

                operations.add(ContentProviderOperation.newInsert(keyWordUri)
                        .withValues(contentValues)
                        .build()
                )
            }

            val contentResolver = getContentResolver()
            contentResolver.applyBatch(DatabaseConstant.AUTHORITY, operations)
            DebugUtil.d(TAG, "addKeyWords recordId:$recordId  size:${keyWords.size}")
        } catch (e: Exception) {
            DebugUtil.e(TAG, "addKeyWord error", e)
        }
    }

    /**
     * 根据录音文件id删除对应的关键词列表
     * @param recordId 录音id
     */
    fun deleteKeyWords(recordId: Long): Int {
        val where = " $RECORD_ID = ? "
        val args = arrayOf(recordId.toString())

        try {
            val contentResolver = getContentResolver()
            val count = contentResolver.delete(keyWordUri, where, args)
            DebugUtil.d(TAG, "deleteKeyWords recordId:$recordId deleteCount:$count")
            return count
        } catch (e: Exception) {
            DebugUtil.e(TAG, "deleteKeyWords error recordId:$recordId", e)
        }
        return 0
    }

    /**
     * 根据主键id删除对应的关键词列表
     * @param id 主键id
     */
    fun deleteKeyWord(id: Int): Int {
        val where = " $ID = ? "
        val args = arrayOf(id.toString())

        try {
            val contentResolver = getContentResolver()
            val count = contentResolver.delete(keyWordUri, where, args)
            DebugUtil.d(TAG, "deleteKeyWord id:$id deleteCount:$count")
            return count
        } catch (e: Exception) {
            DebugUtil.e(TAG, "deleteKeyWord error id:$id", e)
        }
        return 0
    }

    /**
     * 根据录音文件id，查询对应的关键词列表
     * @param recordId 录音文件id
     * @return 关键词列表，默认按照分词权重降序排序，如果没有，则返回空列表
     */
    fun queryKeyWords(recordId: Long): List<KeyWord> {
        val where = " $RECORD_ID = ? "
        val args = arrayOf(recordId.toString())
        val projections = arrayOf(ID, NAME, TFIDF_VALUE)
        val order = " $TFIDF_VALUE DESC " // 降序排序
        var cursor: Cursor? = null
        try {
            val contentResolver = getContentResolver()
            cursor = contentResolver.query(keyWordUri, projections, where, args, order)
            cursor?.let {
                if (it.moveToFirst()) {
                    val idIndex = cursor.getColumnIndexOrThrow(ID)
                    val nameIndex = cursor.getColumnIndexOrThrow(NAME)
                    val tfidfValueIndex = cursor.getColumnIndexOrThrow(TFIDF_VALUE)

                    val list = mutableListOf<KeyWord>()
                    do {
                        val id = cursor.getInt(idIndex)
                        val name = cursor.getString(nameIndex)
                        val tfidfValue = cursor.getFloat(tfidfValueIndex)

                        list.add(KeyWord(name, tfidfValue, id, recordId))
                    } while (cursor.moveToNext())

                    DebugUtil.d(TAG, "queryKeyWords recordId:$recordId  list:${list.size}")
                    return list
                }
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "queryKeyWords error recordId:$recordId", e)
        } finally {
            cursor?.close()
        }

        return emptyList()
    }

    /**
     * 查询所有的关键词
     * @return 关键词列表
     */
    @JvmStatic
    fun queryAllKeyWords(): List<KeyWord> {
        var cursor: Cursor? = null
        try {
            val contentResolver = getContentResolver()
            cursor = contentResolver.query(keyWordUri, null, null, null, null)
            cursor?.let {
                if (it.moveToFirst()) {
                    val idIndex = cursor.getColumnIndexOrThrow(ID)
                    val recordIdIndex = cursor.getColumnIndexOrThrow(RECORD_ID)
                    val nameIndex = cursor.getColumnIndexOrThrow(NAME)
                    val tfidfValueIndex = cursor.getColumnIndexOrThrow(TFIDF_VALUE)

                    val list = mutableListOf<KeyWord>()
                    do {
                        val id = cursor.getInt(idIndex)
                        val recordId = cursor.getLong(recordIdIndex)
                        val name = cursor.getString(nameIndex)
                        val tfidfValue = cursor.getFloat(tfidfValueIndex)

                        list.add(KeyWord(name, tfidfValue, id, recordId))
                    } while (cursor.moveToNext())

                    DebugUtil.d(TAG, "queryAllKeyWords  list:${list.size}")
                    return list
                }
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "queryAllKeyWords error ", e)
        } finally {
            cursor?.close()
        }
        return emptyList()
    }

    /**
     * 更新recordId
     * @param oldRecordId 老的录音文件recordId
     * @param newRecordId 要更新的recordId
     * @return 更新数据的个数
     */
    fun updateRecordId(oldRecordId: Long, newRecordId: Long): Int {
        val where = " $RECORD_ID = ? "
        val args = arrayOf(oldRecordId.toString())

        try {
            val contentResolver = getContentResolver()
            val contentValues = ContentValues().apply {
                put(RECORD_ID, newRecordId)
            }
            val count = contentResolver.update(keyWordUri, contentValues, where, args)
            DebugUtil.d(TAG, "updateRecordId recordId:$oldRecordId -> $newRecordId updateCount:$count")
            return count
        } catch (e: Exception) {
            DebugUtil.e(TAG, "updateRecordId recordId:$oldRecordId -> $newRecordId", e)
        }
        return 0
    }

    /**
     * 批量更新recordId
     * @param list 存放Pair<老的RecordId,新的RecordId> 的列表
     * @return 批量更新的结果
     */
    @JvmStatic
    fun batchUpdateRecordId(list: MutableList<Pair<Long, Long>>): Int {
        if (list.isEmpty()) {
            DebugUtil.d(TAG, "batchUpdateRecordId recordIds is empty")
            return 0
        }
        val where = " $RECORD_ID = ? "
        try {
            val operations = ArrayList<ContentProviderOperation>()
            for (pair in list) {
                val args = arrayOf(pair.first.toString())

                val contentValues = ContentValues().apply {
                    put(RECORD_ID, pair.second)
                }

                operations.add(ContentProviderOperation.newUpdate(keyWordUri)
                        .withSelection(where, args)
                        .withValues(contentValues)
                        .build()
                )
            }

            val contentResolver = getContentResolver()
            val count = contentResolver.applyBatch(DatabaseConstant.AUTHORITY, operations).size
            DebugUtil.d(TAG, "batchUpdateRecordId recordId: $list updateCount:$count")
            return count
        } catch (e: Exception) {
            DebugUtil.e(TAG, "batchUpdateRecordId recordId:$list", e)
        }
        return 0
    }
}