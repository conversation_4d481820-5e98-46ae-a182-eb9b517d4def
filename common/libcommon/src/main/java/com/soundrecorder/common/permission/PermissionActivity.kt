package com.soundrecorder.common.permission

import android.Manifest
import android.app.Activity
import android.app.Dialog
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.Configuration
import androidx.lifecycle.lifecycleScope
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.BaseUtil.isAndroidTOrLater
import com.soundrecorder.base.utils.BaseUtil.isAndroidUOrLater
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.common.base.PrivacyPolicyBaseActivity
import com.soundrecorder.common.permission.PermissionDialogUtils.PermissionDialogListener
import com.soundrecorder.common.permission.PermissionDialogUtils.TYPE_PERMISSION_ALL_FILE_ACCESS
import com.soundrecorder.common.permission.PermissionDialogUtils.TYPE_PERMISSION_OTHER
import com.soundrecorder.common.permission.PermissionDialogUtils.TYPE_PERMISSION_POST_NOTIFICATION
import com.soundrecorder.common.permission.PermissionDialogUtils.showPermissionAllFileAccessDialog
import com.soundrecorder.common.permission.PermissionDialogUtils.showPermissionsDialog
import com.soundrecorder.common.task.ActivityTaskUtils.topActivityWithOutFinishingActivity
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.EditRecordInterface
import com.soundrecorder.modulerouter.HomeInterface
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState
import com.soundrecorder.modulerouter.miniapp.MiniAppInterface
import com.soundrecorder.modulerouter.playback.PlayBackInterface
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.modulerouter.recorder.RecordInterface
import com.soundrecorder.modulerouter.utils.Injector
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

open class PermissionActivity : PrivacyPolicyBaseActivity(), PermissionDialogListener {
    private var hasDeniedOnRequestPermissionsResultAfter = false
    private var hasGrantedOnRequestPermissionsResultAfter = false
    private var mPermissionDialog: Dialog? = null
    private var mPermissionAllFileDialog: Dialog? = null

    private var mPermissionGrantedListener: (() -> Unit)? = null

    //通知权限被拒绝显示受阻snackBar，使用透明activity实现，若snackBar消失回到之前的页面，
    //则在onResume的时候不需要再次查询权限，避免弹窗消失再出现
    private var mIsOnResumeFromNotificationTransparentActivity = false

    /*记录调用requestPermission，结果不为空回来之前为true，若monkey等特殊场景重复调用requestPermission，会将其置为false，下次调用request再为true*/
    protected var requestingPermission: Boolean = false

    private val homeApi by lazy {
        Injector.injectFactory<HomeInterface>()
    }

    private val browseFileApi by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    private val editRecordApi by lazy {
        Injector.injectFactory<EditRecordInterface>()
    }

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    private val recordApi by lazy {
        Injector.injectFactory<RecordInterface>()
    }

    private val miniAppApi by lazy {
        Injector.injectFactory<MiniAppInterface>()
    }

    private val playbackApi by lazy {
        Injector.injectFactory<PlayBackInterface>()
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray,
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        "onRequestPermissionsResult permissions${permissions.contentToString()}".log()
        if (permissions.isEmpty()) {
            /**
             * 切换亮暗色等重建逻辑，onRequestPermissionsResult会回调一个空的permissions
             */
            return
        }
        requestingPermission = false
        PermissionUtils.putRequestPermissions(permissions)
        if (PermissionUtils.hasAllPermissions(permissions, browseFileApi?.isBrowseFile(this) == true)) {
            hasGrantedOnRequestPermissionsResultAfter = true
            mPermissionGrantedListener?.invoke()
        } else {
            hasDeniedOnRequestPermissionsResultAfter = true
            if (homeApi?.isTransparentActivity(this) == true
                || recordApi?.isRecorderActivity(this) == true
                || miniAppApi?.isMiniRecorderActivity(this) == true
            ) {
                checkNeedShowSnackBarRecordScenes(permissions, grantResults)
            } else {
                checkNeedShowSnackBarOtherScenes(permissions, grantResults)
            }
        }
    }

    /**
     * 在非录制场景申请权限被拒绝后，判断被拒绝的权限里面是否有通知权限
     * 有通知权限被且被拒绝，则在当前界面显示受阻弹窗。
     */
    private fun checkNeedShowSnackBarOtherScenes(
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        "权限拒绝-其他场景: $permissions ,grantResults: $grantResults".log()
        for (i in permissions.indices) {
            if (PermissionUtils.POST_NOTIFICATIONS == permissions[i]) {
                //被拒绝的权限组有通知权限，且被拒绝了
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    showRequestNotificationPermissionSnackBar(this)
                }
                break
            }
        }
    }

    /**
     * 在录制场景申请权限被拒绝后，判断被拒绝的权限里面是否有通知权限
     * 有通知权限被且被拒绝，则判断是否满足继续录制的条件
     * 满足继续录制条件则在录制界面显示受阻弹窗，不满足则回到首页显示受阻弹窗
     * 侧边栏场景则是在本次录制流程内，首次进入录制界面展示弹窗，如果本次不展示，后续也不再展示。
     */
    private fun checkNeedShowSnackBarRecordScenes(
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        "权限拒绝-录制场景: ${permissions.contentToString()} ,grantResults: ${grantResults.contentToString()}".log()
        var shouldFinish = false
        var shouldShowSnackBar = false
        for (i in permissions.indices) {
            if (PermissionUtils.READ_AUDIO_PERMISSION() == permissions[i] || PermissionUtils.RECORD_AUDIO == permissions[i]) {
                //被拒绝的权限组里面有存储/麦克风权限，且被拒绝了
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    shouldFinish = true
                }
            }
            if (PermissionUtils.POST_NOTIFICATIONS == permissions[i]) {
                //被拒绝的权限组有通知权限，且被拒绝了
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    shouldShowSnackBar = true
                }
            }
        }
        if (shouldFinish) {
            if (shouldShowSnackBar) {
                showNotificationPermissionSnackBar(true)
            }
            doFinishActivityWhenRefusePermission()
        } else {
            //有读写权限和录制权限继续录制
            //如果是TransparentActivity，则在TransparentActivity的onPermissionGranted回调中处理
            //侧边栏透明界面场景在本次活动中首次进入录制界面后再展示
            hasGrantedOnRequestPermissionsResultAfter = true
            mPermissionGrantedListener?.invoke()
            if (shouldShowSnackBar) {
                showNotificationPermissionSnackBar(false)
            }
        }
    }

    open fun showNotificationPermissionSnackBar(shouldFinish: Boolean = false) {
        val isRecordActivity = recordApi?.isRecorderActivity(this) ?: false
        val isMiniRecordActivity = miniAppApi?.isMiniRecorderActivity(this) ?: false
        val needShowSnackBar = PermissionUtils.isNeedShowNotificationPermissionSnackBar(this)
        if (shouldFinish) {
            if (isRecordActivity) {
                //录制界面被拒绝，回到首页显示snackBar
                setResult(PermissionUtils.RESULT_NOTIFICATION_PERMISSION_DENIED)
            } else if (isMiniRecordActivity) {
                // 火烈鸟外屏显示通知弹窗
                if (needShowSnackBar) {
                    mPermissionDialog =
                        showPermissionsDialog(this, this, arrayOf(Manifest.permission.POST_NOTIFICATIONS), permissionDialogStyleType())
                }
            } else {
                //侧边栏被拒绝，录制service不会开始，不再显示snackBar
                PermissionUtils.setHasShowNotificationPermissionSnackBar(this)
            }
        } else if (needShowSnackBar) {
            if (isRecordActivity) {
                //录制界面，且没有展示过snackBar
                showRequestNotificationPermissionSnackBar(this)
            } else if (isMiniRecordActivity) {
                mPermissionDialog = showPermissionsDialog(this, this, arrayOf(Manifest.permission.POST_NOTIFICATIONS), permissionDialogStyleType())
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == PermissionUtils.REQUEST_CODE_PERMISSION_TRANSPARENT) {
            //snackBar消失的场景，不需要在onResume里面再次检查权限.否则会重新弹出权限受阻弹窗
            mIsOnResumeFromNotificationTransparentActivity = true
        }
    }

    @Suppress("NestedBlockDepth")
    protected fun requestPermission() {
        releasePermissionAllFileDialog()
        releasePermissionDialog()
        if (checkCanRequestPermission()) {
            val isBrowseFile = browseFileApi?.isBrowseFile(this) ?: false
            val permissions = configPermission()
            "top activity is current activity,should requestPermission ${permissions.contentToString()}".log()
            if (PermissionUtils.hasAllPermissions(permissions, isBrowseFile)) {
                /**
                 * OnRequestPermissionsResult授权granted后，已经执行了mPermissionGrantedListener?.invoke(),无需重复执行
                 */
                if (hasGrantedOnRequestPermissionsResultAfter) {
                    hasGrantedOnRequestPermissionsResultAfter = false
                } else {
                    mPermissionGrantedListener?.invoke()
                }
            } else {
                if (PermissionUtils.cannotRequestPermissions(this, permissions)) {
                    if (!canShowPermissionsDialogWhenCannotRequestPermissions()) {
                        "BrowseFile do not show permissions dialog".log()
                    } else {
                        mPermissionDialog = showPermissionsDialog(this, this, permissions, permissionDialogStyleType())
                    }
                } else if (PermissionUtils.hasPermissionsRationale(this, permissions)) {
                    if (recordApi?.isRecorderActivity(this) == true || homeApi?.isTransparentActivity(this) == true
                        || miniAppApi?.isMiniRecorderActivity(this) == true
                    ) {
                        doRequestPermissionsFromSystem(permissions)
                    } else {
                        if (hasDeniedOnRequestPermissionsResultAfter) {
                            if (canShowPermissionsDialogWhenHasPermissionsRationale()) {
                                mPermissionDialog = showPermissionsDialog(this, this, permissions, permissionDialogStyleType())
                            } else {
                                "BrowseFile do not request permission".log()
                            }
                        } else {
                            doRequestPermissionsFromSystem(permissions)
                        }
                    }
                } else {
                    doRequestPermissionsFromSystem(permissions)
                }
            }
        } else {
            "not top activity,so not requestPermission".log()
        }
    }

    /**
     * 走系统申请权限
     */
    protected open fun doRequestPermissionsFromSystem(permissions: Array<String>) {
        if (permissions.isEmpty()) {
            DebugUtil.w(TAG, "doRequestPermissionsFromSystem return by empty")
            return
        }
        if (requestingPermission) {
            // 正常情况若在显示运行时弹窗，不会再走onResume，避免异常，若为true，将其置为false，使其下一次正常调用
            DebugUtil.w(TAG, "doRequestPermissionsFromSystem return by requestingPermission")
            requestingPermission = false
            return
        }
        DebugUtil.d(TAG, "doRequestPermissionsFromSystem start")
        PermissionUtils.requestPermissions(this, permissions, requestCode())
        requestingPermission = true
    }

    protected open fun checkCanRequestPermission(): Boolean {
        val topActivity = topActivityWithOutFinishingActivity()
        return topActivity == null || topActivity === this
    }

    /**
     * 替代原来的isBrowseFile(),若为BrowseFile, 需返回false
     * */
    open fun canShowPermissionsDialogWhenCannotRequestPermissions(): Boolean = true

    /**
     * 替代原来的isBrowseFile(),若为BrowseFile, 需返回false
     * */
    open fun canShowPermissionsDialogWhenHasPermissionsRationale(): Boolean = true

    /**
     * 权限弹窗显示样式
     * @see PermissionDialogUtils.TYPE_DIALOG_DEFAULT 主要用于内屏权限弹窗
     * @see PermissionDialogUtils.TYPE_DIALOG_TINY  用于外屏权限弹窗
     */
    open fun permissionDialogStyleType(): Int = PermissionDialogUtils.TYPE_DIALOG_DEFAULT

    protected open fun configPermission(): Array<String> {
        val permissionList = ArrayList<String>()
        if (!PermissionUtils.hasReadAudioPermission()) {
            if (BaseUtil.isAndroidROrLater) {
                permissionList.add(PermissionUtils.READ_AUDIO_PERMISSION())
            } else {
                permissionList.addAll(PermissionUtils.STORAGE_PERMISSIONS_Q)
            }
        }

        //首页Android U及以后不需要再申请照片和视频权限
        if (browseFileApi?.isBrowseFile(this) == true
            && PermissionUtils.shouldFirstRequestReadImagesPermission(this)
            && !isAndroidUOrLater
        ) {
            permissionList.add(PermissionUtils.READ_MEDIA_IMAGES)
        }
        if (recordApi?.isRecorderActivity(this) == true
            || homeApi?.isTransparentActivity(this) == true
            || miniAppApi?.isMiniRecorderActivity(this) == true
        ) {
            if (!PermissionUtils.hasRecordAudioPermission()) {
                permissionList.add(PermissionUtils.RECORD_AUDIO)
            }
            if (isAndroidTOrLater && !PermissionUtils.hasNotificationPermission() &&
                !PermissionUtils.hasRequestNotificationPermission(this)
            ) {
                permissionList.add(PermissionUtils.POST_NOTIFICATIONS)
            }
        }
        val size = permissionList.size
        return if (size <= 0) {
            arrayOf()
        } else {
            permissionList.toTypedArray()
        }
    }

    protected open fun requestCode(): Int {
        return when {
            browseFileApi?.isBrowseFile(this) == true -> PermissionUtils.REQUEST_CODE_BROWSE_FILE
            recordApi?.isRecorderActivity(this) == true -> PermissionUtils.REQUEST_CODE_RECORDER
            playbackApi?.isPlaybackActivity(this) == true -> PermissionUtils.REQUEST_CODE_PLAY_BACK
            editRecordApi?.isEditRecordActivity(this) == true -> PermissionUtils.REQUEST_CODE_EDIT_RECORD
            homeApi?.isTransparentActivity(this) == true -> PermissionUtils.REQUEST_CODE_TRANSPARENT
            miniAppApi?.isMiniRecorderActivity(this) == true -> PermissionUtils.REQUEST_CODE_MINI_RECORDER
            else -> PermissionUtils.REQUEST_CODE_SETTINGS
        }
    }

    fun setPermissionGrantedListener(permissionGrantedListener: () -> Unit) {
        mPermissionGrantedListener = permissionGrantedListener
    }

    override fun onClick(alertType: Int, isOk: Boolean, permissions: ArrayList<String>?) {
        when (alertType) {
            TYPE_PERMISSION_OTHER -> {
                releasePermissionDialog()
                if (isOk) {
                    goToAppSettingConfigurePermissions(alertType, permissions)
                } else {
                    doFinishActivityWhenRefusePermission()
                }
            }

            TYPE_PERMISSION_ALL_FILE_ACCESS -> {
                PermissionUtils.setNextActionForRequestPermission(this)
                releasePermissionAllFileDialog()
                if (isOk) {
                    PermissionUtils.goToAppAllFileAccessConfigurePermissions(this)
                } else {
                    /* 加这个判断是为了更新slLastClickTime的值，防止点击取消的同时又去点击录制按钮进入录制界面 */
                    if (!ClickUtils.isFastDoubleClick()) {
                        //如果收起启动没有开启文件访问权限，云同步开关不能打开
                        switchCloudOff()
                        requestPermission()
                    }
                }
            }

            TYPE_PERMISSION_POST_NOTIFICATION -> {
                releasePermissionDialog()
                if (isOk) {
                    goToAppSettingConfigurePermissions(alertType, permissions)
                } else {
                    DebugUtil.d(TAG, "onClick TYPE_PERMISSION_POST_NOTIFICATION dialog cancel")
                    // 点击取消，设置已经显示过通知权限受阻弹窗，内屏不用再弹受阻弹窗了
                    PermissionUtils.setHasShowNotificationPermissionSnackBar(this)
                }
            }

            else -> {}
        }
    }

    /**
     * 如果没有授予所有文件权限，关闭云同步开关
     */
    private fun switchCloudOff() {
        val isRegionAndSupportCloud: Boolean = cloudKitApi?.isSupportCloudArea() == true && cloudKitApi?.isSupportSwitch() == true
        if (!NetworkUtils.isNetworkInvalid(this) && isRegionAndSupportCloud && OS12FeatureUtil.isColorOS14OrLater()) {
            this.lifecycleScope.launch(Dispatchers.IO) {
                val isSuccess = cloudKitApi?.setSyncSwitch(CloudSwitchState.CLOSE, false)
                DebugUtil.v(TAG, "switchCloudOff, isSuccess:$isSuccess")
            }
        }
    }

    protected open fun goToAppSettingConfigurePermissions(alertType: Int, permissions: ArrayList<String>? = null) {
        PermissionUtils.goToAppSettingConfigurePermissions(this, permissions)
    }

    open fun doFinishActivityWhenRefusePermission() {
        finish()
    }

    override fun onBackPress(alertType: Int) {
        finish()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (recordApi?.isRecorderActivity(this) == true && ((PermissionUtils.getNextAction() != PermissionUtils.SHOULD_SHOW_USER_NOTICE))) {
            requestPermission()
        }
    }

    override fun onDestroy() {
        releasePermissionAllFileDialog()
        releasePermissionDialog()
        super.onDestroy()
    }

    override fun onAgreeClick() {
        if (!BaseUtil.isAndroidROrLater) {
            requestPermission()
            return
        }
        if (PermissionUtils.hasAllFilePermission()) {
            requestPermission()
        } else {
            if (!isAllFileDialogShowing()) {
                mPermissionAllFileDialog = showPermissionAllFileAccessDialog(this, this)
            }
        }
    }

    fun isAllFileDialogShowing(): Boolean {
        return mPermissionAllFileDialog?.isShowing == true
    }

    override fun afterCheckAndShowPrivacyPolicyDialogOnResume(nextAction: Int) {
        if (mIsOnResumeFromNotificationTransparentActivity) {
            mIsOnResumeFromNotificationTransparentActivity = false
            return
        }
        releasePermissionDialog()
        when (nextAction) {
            PermissionUtils.SHOULD_SHOW_ALL_FILE_PERMISSION -> checkRequestAllFilePermission()
            PermissionUtils.SHOULD_REQUEST_PERMISSIONS -> requestPermission()
        }
    }

    override fun onPrivacyPolicyFail(type: Int, pageFrom: Int?) {
        when (type) {
            PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE -> {
                val dialogStatus: Int = PermissionUtils.getNextAction()
                if (dialogStatus == PermissionUtils.SHOULD_SHOW_ALL_FILE_PERMISSION) {
                    checkRequestAllFilePermission()
                } else if (dialogStatus == PermissionUtils.SHOULD_REQUEST_PERMISSIONS) {
                    requestPermission()
                }
            }
        }
    }

    protected fun isPermissionDialogShowing(): Boolean = mPermissionDialog?.isShowing == true

    private fun releasePermissionDialog() {
        if (mPermissionDialog?.isShowing == true) {
            mPermissionDialog?.dismiss()
        }
        mPermissionDialog = null
    }

    private fun releasePermissionAllFileDialog() {
        if (mPermissionAllFileDialog?.isShowing == true) {
            mPermissionAllFileDialog?.dismiss()
        }
        mPermissionAllFileDialog = null
    }

    private fun checkRequestAllFilePermission() {
        val funcNext = {
            if (BaseUtil.isEXP()) {
                PermissionUtils.setNextActionForRequestPermission(this)
            }
            requestPermission()
        }
        if (!BaseUtil.isAndroidROrLater) {
            // Q及以下无所有文件管理权限
            funcNext.invoke()
            return
        }
        if (PermissionUtils.hasAllFilePermission()) {
            funcNext.invoke()
        } else {
            if (mPermissionAllFileDialog?.isShowing == true) {
                "mPermissionAllFileDialog is showing".log()
            } else {
                mPermissionAllFileDialog = showPermissionAllFileAccessDialog(this, this)
            }
        }
    }

    private fun Any.log() {
        DebugUtil.i("$TAG-${<EMAIL>}", "$this")
    }

    companion object {
        private const val TAG = "PermissionActivity"

        fun showRequestNotificationPermissionSnackBar(activity: Activity?) {
            if (activity == null) {
                DebugUtil.i(TAG, "activity = null！")
                return
            }
            if (PermissionUtils.hasShowNotificationPermissionSnackBar(BaseApplication.getAppContext())) {
                DebugUtil.i(TAG, "已经显示过权限受阻弹窗！")
                return
            }
            showRequestNotificationPermissionSnackBarWithoutCheck(activity)
        }

        @JvmStatic
        fun showRequestNotificationPermissionSnackBarWithoutCheck(activity: Activity?) {
            if (activity == null) {
                DebugUtil.i(TAG, "activity = null！")
                return
            }
            startNotificationPermissionTransparentActivity(activity)
            PermissionUtils.setHasShowNotificationPermissionSnackBar(activity)
        }

        /**
         * 使用透明activity展示snackBar，方便设置点击空白区域消失和点击返回按钮消失。
         */
        private fun startNotificationPermissionTransparentActivity(activity: Activity) {
            val intent = Intent(
                activity, NotificationPermissionSnackBarTransparentActivity::class.java
            )
            val color = activity.window.navigationBarColor
            intent.putExtra("color", color)
            activity.startActivityForResult(
                intent,
                PermissionUtils.REQUEST_CODE_PERMISSION_TRANSPARENT
            )
        }
    }
}