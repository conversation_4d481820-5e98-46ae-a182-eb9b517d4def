/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: DeleteFileDialogUtil
 * Description:
 * Version: 1.0
 * Date: 2022/10/31
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/10/31 1.0 create
 */

package com.soundrecorder.common.fileoperator.delete

import android.app.Activity
import android.app.KeyguardManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.common.R
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.db.GroupInfoManager
import com.soundrecorder.common.executor.ExecutorManager
import com.soundrecorder.common.fileoperator.CheckOperatorWithPermission
import com.soundrecorder.common.utils.FileDealUtil
import com.soundrecorder.common.utils.sound.DeleteSoundEffectManager
import com.soundrecorder.modulerouter.SeedingInterface
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.convertService.ConvertThreadManageAction
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import com.soundrecorder.modulerouter.utils.Injector
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class DeleteFileDialogUtil(private val onFileDeleteListener: OnFileDeleteListener? = null) {
    companion object {
        private const val ACTION_LOCK_SCREEN = "android.app.action.CONFIRM_DEVICE_CREDENTIAL"
        const val LOCK_SCREEN_START_TYPE = "start_type"
        const val LOCK_SCREEN_CUSTOMIZE_HEAD = "customize_head"
        const val LOCK_SCREEN_CUSTOMIZE_HEAD_STR = "customize_head_str"
        const val LOCK_SCREEN_CUSTOMIZE_HEAD_STR_PSWD = "customize_head_str_password"
        const val REQUEST_LOCK_SCREEN_RESULT_SUCCESS = 1
        /*回收站非编辑模式下底部全部删除按钮*/
        const val REQUEST_LOCK_SCREEN_RECYCLE_DELETE_ALL_NONE_EDIT = 2

        /*标记： 编辑模式下 删除按钮 弹出的dialog*/
        const val DIALOG_TYPE_NORMAL = 0X1
        /*标记： 回收站【非编辑模式】下底部全部删除按钮 弹出的dialog*/
        const val DIALOG_TYPE_RECYCLE_DELETE_ALL_NONE_EDIT = 0X2
    }

    private val mLogTag = "DeleteFileDialogUtil"
    private var mOperatePermission: CheckOperatorWithPermission? = null

    private val seedingApi by lazy {
        Injector.injectFactory<SeedingInterface>()
    }

    private val convertThreadManageAction by lazy {
        Injector.injectFactory<ConvertThreadManageAction>()
    }

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    private val smartNameAction by lazy {
        Injector.injectFactory<SmartNameAction>()
    }

    /**
     * @param activity
     * @param beDeleteRecordList
     * @param deleteAll
     * @param isRecycle 是否从回收站删除
     */
    fun delete(activity: Activity, beDeleteRecordList: List<Record>, deleteAll: Boolean, isRecycle: Boolean) {
        if (deleteAll && beDeleteRecordList.size > 1) {
            deleteAllRecord(activity, beDeleteRecordList, isRecycle)
        } else {
            deleteWithPermission(activity, beDeleteRecordList, true, isRecycle)
        }
    }

    fun deleteWithPermission(activity: Activity, beDeleteRecordList: List<Record>, deleteHasPermission: Boolean, isRecycle: Boolean) {
        if (mOperatePermission == null) {
            mOperatePermission = CheckOperatorWithPermission(activity)
        }
        if (deleteHasPermission) {
            mOperatePermission?.dismissAllFileDialog()
        }
        var bNeedContinueCompare = false
        if (cloudKitApi?.isMediaComparing() == true) {
            cloudKitApi?.doStopMediaCompare(true)
            bNeedContinueCompare = true
            DebugUtil.i(
                "DeleteFileDialogUtil",
                "mediaComparing, need to stop diff compare when operate batch: deleteRecords!"
            )
        }

        mOperatePermission?.deleteRecords(onFileDeleteListener?.provideDeleteRequestCode(), beDeleteRecordList, deleteHasPermission) {
            CoroutineScope(Dispatchers.IO).launch {
                DebugUtil.d(mLogTag, "onDeleteFileBefore")
                onFileDeleteListener?.onDeleteFileBefore()
                val deleteTime = System.currentTimeMillis()
                val deleteSuccessCount = deleteRecord(activity, beDeleteRecordList, !deleteHasPermission, isRecycle)
                val deleteCost = System.currentTimeMillis() - deleteTime
                DebugUtil.d(mLogTag, "deleteRecord: time=$deleteCost")

                onFileDeleteListener?.onDeleteFileResult(deleteSuccessCount == beDeleteRecordList.size)

                val handleTime = System.currentTimeMillis()
                //bugfix: 先处理历史问题产生的僵尸文件
                FileDealUtil.handleZombieFilesInRecycleBin()
                val handleCost = System.currentTimeMillis() - handleTime
                DebugUtil.d(mLogTag, "handleZombieFilesInRecycleBin: time=$handleCost")

                //如果是从回收站列表里彻底删除，肯定不存在转文本的任务，只有“全部录音”或者”通话录音“才可能有
                if (!isRecycle) {
                    //如果删除的录音文件正在转录文本，立即取消其转文本的任务
                    beDeleteRecordList.forEach {
                        convertThreadManageAction?.cancelConvert(it.id)
                        smartNameAction?.cancelSmartNameTask(it.id)
                    }
                }
                if (bNeedContinueCompare) {
                    ExecutorManager.recordDataSyncExecutor?.execute {
                        cloudKitApi?.doMediaCompare(false)
                        DebugUtil.i(
                            "DeleteFileDialogUtil",
                            "mediaComparing, Continue to diff compare after operate batch: deleteRecords!"
                        )
                    }
                }
                //删除记录后，刷新一下分组信息，避免在云同步关闭的情况下，被删除的分组依然再分组列表中显示
                GroupInfoManager.getInstance(BaseApplication.getAppContext()).refreshAllGroupInfoAfterDataChanged()
                CloudStaticsUtil.addCloudLog("DeleteFileDialogUtil", "deleteWithPermission, permission= $deleteHasPermission, " +
                        "deleteSize=${beDeleteRecordList.size}, success count=$deleteSuccessCount")
            }
        }
    }

    /**
     * @param audioIsBeDeleted 音频文件是否已经被删除了
     * @param isRecycle 是否从回收站删除
     */
    private fun deleteRecord(activity: Activity, beDeleteRecordList: List<Record>, audioIsBeDeleted: Boolean, isRecycle: Boolean): Int {
        var index = 0
        var deleteSuccessCount = 0
        if (audioIsBeDeleted) {
            /*音频文件已经被系统删除了，清除录音自己数据库的相关内容，此场景针对于：无权限，允许系统弹窗删除音频文件*/
            if (beDeleteRecordList.size > 1) {
                FileDealUtil.deleteRecordDBBatch(beDeleteRecordList)
            } else {
                // 这里是由于播放过来的record字段不完善
                val targetRecord: Record = beDeleteRecordList[0]
                FileDealUtil.deleteRecordDBBatch(activity, targetRecord.data, targetRecord.id)
            }
            deleteSuccessCount = beDeleteRecordList.size
        } else {
            while (!Thread.currentThread().isInterrupted && (index < beDeleteRecordList.size)) {
                val targetRecord: Record = beDeleteRecordList[index]
                FileDealUtil.deleteRecord(activity, targetRecord, onFileDeleteListener?.provideDeleteRequestCode(), isRecycle)
                    .run {
                        if (this) {
                            deleteSuccessCount++
                        }
                    }
                index++
                DebugUtil.i(mLogTag, "delete index:$index")
            }
        }
        if (deleteSuccessCount > 0) {
            DeleteSoundEffectManager.getInstance().playDeleteSound()
        }

        DebugUtil.i(mLogTag, "audioIsBeDeleted $audioIsBeDeleted, delete success:$deleteSuccessCount")
        seedingApi?.sendRecordDeleteEvent()
        return deleteSuccessCount
    }

    private fun deleteAllRecord(activity: Activity, beDeleteRecordList: List<Record>, isRecycle: Boolean) {
        val context = activity
        val km = activity.getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
        val isInChildMode = FeatureOption.checkInChildrenMode(BaseApplication.getAppContext())
        if (km.isDeviceSecure && !isInChildMode) {
            DebugUtil.d(mLogTag, "[onClick] show lock screen")
            val recordingType = context.getString(R.string.normal_recording_tab)
            val textHint: String = context.getString(R.string.delete_all_item_text_tip, recordingType)
            val patternHint: String = context.getString(R.string.delete_all_item_pattern_tip, recordingType)
            try {
                val intent = Intent(ACTION_LOCK_SCREEN)
                intent.putExtra(LOCK_SCREEN_START_TYPE, LOCK_SCREEN_CUSTOMIZE_HEAD)
                intent.putExtra(LOCK_SCREEN_CUSTOMIZE_HEAD_STR, patternHint)
                intent.putExtra(LOCK_SCREEN_CUSTOMIZE_HEAD_STR_PSWD, textHint)
                val pm: PackageManager = context.packageManager
                val resolveInfos = pm.queryIntentActivities(intent, PackageManager.GET_RESOLVED_FILTER)
                if (resolveInfos.size != 0) {
                    val activityInfo = resolveInfos[0].activityInfo
                    if (activityInfo != null) {
                        val packageName = activityInfo.packageName
                        if (packageName != null) {
                            intent.setPackage(packageName)
                            DebugUtil.i(mLogTag, "Intent to $packageName")
                        }
                    }
                }
                val requestCode = onFileDeleteListener?.provideLockScreenDeleteRequestCode()
                        ?: REQUEST_LOCK_SCREEN_RESULT_SUCCESS
                activity.startActivityForResult(intent, requestCode)
            } catch (ignored: Exception) {
                DebugUtil.e(mLogTag, "start lock screen faid!", ignored)
                deleteWithPermission(activity, beDeleteRecordList, true, isRecycle)
            }
        } else {
            deleteWithPermission(activity, beDeleteRecordList, true, isRecycle)
        }
    }

    fun getOperating(): Boolean = mOperatePermission?.getOperating() ?: false

    fun resetOperating() {
        mOperatePermission?.resetContinueOperator()
    }

    fun release() {
        mOperatePermission?.release()
        mOperatePermission = null
    }
}

interface OnFileDeleteListener {
    fun onDeleteFileBefore() {}
    fun onDeleteFileResult(deleteSuccess: Boolean) {}
    fun provideDeleteRequestCode(): Int? = null
    fun provideLockScreenDeleteRequestCode(): Int = DeleteFileDialogUtil.REQUEST_LOCK_SCREEN_RESULT_SUCCESS
}