package com.soundrecorder.common.utils

import android.annotation.SuppressLint
import android.content.res.Resources
import android.graphics.Outline
import android.graphics.Rect
import android.util.DisplayMetrics
import android.util.Size
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.view.ViewOutlineProvider
import android.view.Window
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DimenRes
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.ViewCompat
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.pressfeedback.COUIPressFeedbackHelper
import com.oplus.anim.EffectiveAnimationView
import com.oplus.graphics.OplusOutline
import com.oplus.graphics.OplusOutlineAdapter
import com.oplus.view.OplusSmoothRoundedManager
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.common.R
import kotlin.math.abs


object ViewUtils {

    private const val TAG = "ViewUtils"

    /*
     * just only not child view this view use setAnimatePressBackground has ok
     */
    @SuppressLint("ClickableViewAccessibility")
    @JvmStatic
    fun View.setAnimatePressBackground() {
        val feedbackUtils = COUIPressFeedbackHelper(this, COUIPressFeedbackHelper.CARD_PRESS_FEEDBACK)
        this.setOnTouchListener { _, event ->
            when (event?.action) {
                MotionEvent.ACTION_DOWN -> feedbackUtils.executeFeedbackAnimator(true)
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> feedbackUtils.executeFeedbackAnimator(false)
            }
            return@setOnTouchListener false
        }
    }

    /*
     * dp to pix
     */
    @JvmStatic
    fun dp2px(dpValue: Float = 4f, isFollowScreenLayout: Boolean = true): Float {
        val scale = if (isFollowScreenLayout) Resources.getSystem().displayMetrics.density
        else DisplayMetrics.DENSITY_DEVICE_STABLE / DisplayMetrics.DENSITY_DEFAULT.toFloat()
        return dpValue * scale
    }

    @JvmStatic
    fun resetAnimator(v: View) {
        v.apply {
            alpha = 1f
            scaleY = 1f
            scaleX = 1f
            animate().setInterpolator(null).startDelay = 0
        }
    }

    /**
     * 控件给的方案，
     * 解决AlertDialog弹出后，
     * home键退出回来状态栏和底部导航闪现黑色背景问题
     */
    @JvmStatic
    fun updateWindowLayoutParams(window: Window?) {
        window?.let {
            val lp = it.attributes
            lp.height = ViewGroup.LayoutParams.WRAP_CONTENT
            it.attributes = lp
        }
    }

    inline fun View.doOnAttachState(crossinline action: (view: View, state: Boolean) -> Unit) {
        action.invoke(this, ViewCompat.isAttachedToWindow(this))
        addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(view: View) {
                action(view, true)
            }

            override fun onViewDetachedFromWindow(view: View) {
                action.invoke(view, false)
                removeOnAttachStateChangeListener(this)
            }
        })
    }

    @JvmStatic
    fun View.getUnDisplayViewHeight(): Int {
        val width = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        val height = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        measure(width, height)
        return measuredHeight
    }

    @JvmStatic
    fun View.getUnDisplayViewSize(): Size {
        val width = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        val height = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        measure(width, height)
        return Size(measuredWidth, measuredHeight)
    }

    @JvmStatic
    fun ImageView.onRelease() {
        if (this is EffectiveAnimationView) {
            if (isAnimating) {
                cancelAnimation()
            }
            setImageDrawable(null)
        }
    }

    @JvmStatic
    inline fun View.doOnLayoutChange(crossinline onLayout: (view: View, newRect: Rect, oldRect: Rect) -> Unit) {
        val newRect = Rect()
        val oldRect = Rect()
        val listener = View.OnLayoutChangeListener { v, l, t, r, b, ll, tt, rr, bb ->
            newRect.set(l, t, r, b)
            oldRect.set(ll, tt, rr, bb)
            if (newRect != oldRect) {
                onLayout(v, newRect, oldRect)
            }
        }
        addOnLayoutChangeListener(listener)
        addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(view: View) {}

            override fun onViewDetachedFromWindow(view: View) {
                removeOnAttachStateChangeListener(this)
                removeOnLayoutChangeListener(listener)
            }
        })
    }

    fun View.isOutOfBounds(event: MotionEvent): Boolean {
        val x = event.x.toInt()
        val y = event.y.toInt()
        val slop = ViewConfiguration.get(context).scaledWindowTouchSlop
        return (x < -slop || y < -slop
                || x > width + slop
                || y > height + slop)
    }

    @JvmStatic
    fun RecyclerView?.findAnchor(): View? {
        if (this?.isVisible != true) return null
        val manager = (layoutManager as? LinearLayoutManager) ?: return null
        val firstVisibleItemPosition = manager.findFirstVisibleItemPosition()
        if (firstVisibleItemPosition == RecyclerView.NO_POSITION) {
            return null
        }
        val lastVisibleItemPosition = manager.findLastVisibleItemPosition()
        if (lastVisibleItemPosition == RecyclerView.NO_POSITION) {
            return null
        }
        val visibleItemCount = lastVisibleItemPosition - firstVisibleItemPosition + 1
        if (visibleItemCount <= 0) {
            return null
        }
        val firstCompletelyVisibleItemPosition = manager.findFirstCompletelyVisibleItemPosition()
        val targetIndex = if (visibleItemCount == 1) {
            firstVisibleItemPosition
        } else if (visibleItemCount == 2) {
            if (firstCompletelyVisibleItemPosition > RecyclerView.NO_POSITION) {
                firstCompletelyVisibleItemPosition
            } else {
                lastVisibleItemPosition
            }
        } else {
            firstCompletelyVisibleItemPosition
        }
        return manager.findViewByPosition(targetIndex)
    }

    @JvmStatic
    fun TextView.fixTextFlash(text: String) {
        this.setTag(R.id.tag_textview_old_length, length())
        setText(text)
        val min = dp2px(24f)
        val function = fun(force: Boolean) {
            val newText = this.text.toString()
            val newLength = newText.length
            val oLength = this.getTag(R.id.tag_textview_old_length) as? Int
            gravity = Gravity.LEFT or Gravity.CENTER_VERTICAL
            val textWidth = paint.measureText(newText)
            val paddingS = ((width - textWidth) / 2).toInt()
            if (newLength != oLength || force) {
                updatePadding(paddingS)
            } else {
                if (abs(paddingS - paddingLeft) > min) {
                    updatePadding(paddingS)
                }
            }
        }
        if (width <= 0) {
            if (this.getTag(R.id.tag_textview_by_layout_change) != null) return
            this.setTag(R.id.tag_textview_by_layout_change, this)
            doOnLayoutChange { _, _, _ ->
                function.invoke(true)
            }
            return
        }
        function.invoke(false)
    }

    @JvmStatic
    fun RecyclerView.addItemDecoration(@DimenRes res: Int) {
        addItemDecoration(res, 0)
    }

    @JvmStatic
    fun RecyclerView.addItemDecoration(@DimenRes res: Int, fromIndex: Int = 0) {
        if (getTag(R.id.tag_recyclerview_add_item_decoration) != null) return
        addItemDecoration(object : RecyclerView.ItemDecoration() {
            init {
                <EMAIL>(R.id.tag_recyclerview_add_item_decoration, this)
            }

            val dividerHeight = resources.getDimensionPixelOffset(res)
            override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
                outRect.set(0, 0, 0, 0)
                val index = parent.getChildAdapterPosition(view)
                if (index <= fromIndex) {
                    outRect.set(0, 0, 0, 0)
                } else {
                    outRect.set(0, dividerHeight, 0, 0)
                }
            }
        })
    }

    @JvmStatic
    fun RecyclerView.addItemDecorationBottom(@DimenRes res: Int) {
        if (getTag(R.id.tag_recyclerview_add_item_decoration) != null) return
        addItemDecoration(object : RecyclerView.ItemDecoration() {
            init {
                <EMAIL>(R.id.tag_recyclerview_add_item_decoration, this)
            }

            val dividerHeight = resources.getDimensionPixelOffset(res)
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                outRect.set(0, 0, 0, dividerHeight)
            }
        })
    }

    @JvmStatic
    fun View.updateConstraintHeight(value: Int) {
        (layoutParams as? ConstraintLayout.LayoutParams)?.height?.run {
            if (this != value) {
                updateLayoutParams<ConstraintLayout.LayoutParams> {
                    height = value
                }
            }
        }
    }

    @JvmStatic
    fun View.updateConstraintPercentWidth(percent: Float) {
        (layoutParams as? ConstraintLayout.LayoutParams)?.matchConstraintPercentWidth?.let {
            if (it != percent) {
                updateLayoutParams<ConstraintLayout.LayoutParams> {
                    matchConstraintPercentWidth = percent
                }
            }
        }
    }

    @JvmStatic
    fun View.changeScaleXY(value: Float) {
        if (scaleX != value) {
            scaleX = value
        }
        if (scaleY != value) {
            scaleY = value
        }
    }

    @JvmStatic
    fun View.changeTransitionY(value: Float) {
        if (translationY != value) {
            translationY = value
        }
    }

    @JvmStatic
    fun View.setSmoothRoundCorner(radius: Float) {
        if (OS12FeatureUtil.isColorOS16OrLater() && isFullSmoothOn()) {
            outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View?, outline: Outline?) {
                    //NEW_OUTLINE_SMOOTH:表示16新平滑圆角
                    kotlin.runCatching {
                        val oplusOutline = OplusOutlineAdapter(outline, OplusOutlineAdapter.NEW_OUTLINE_SMOOTH)
                        oplusOutline.setSmoothRoundRect(0, 0, width, height, radius)
                    }.onFailure {
                        DebugUtil.e(TAG, "setSmoothRoundCorner on os16 :  error: ${it.message}")
                    }
                }
            }
            //clipToOutline = true
        } else if (OS12FeatureUtil.isColorOS15OrLater() && isSmoothRadiusOn()) {
            outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    kotlin.runCatching {
                        val oplusOutline = OplusOutline(outline)
                        oplusOutline.setSmoothRoundRect(0, 0, width, height, radius, NumberConstant.NUM_F0_98)
                    }.onFailure {
                        DebugUtil.e(TAG, "setSmoothRoundCorner on os15 :  error: ${it.message}")
                    }
                }
            }
            //clipToOutline = true
            invalidateOutline()
        }
    }

    @JvmStatic
    private fun isFullSmoothOn(): Boolean {
        kotlin.runCatching {
            return OplusSmoothRoundedManager.isFullSmoothOn()
        }.onFailure {
            DebugUtil.e(TAG, "isFullSmoothOn error: ${it.message}")
        }
        return false
    }

    @JvmStatic
    private fun isSmoothRadiusOn(): Boolean {
        kotlin.runCatching {
            return OplusSmoothRoundedManager.isSmoothRadiusOn()
        }.onFailure {
            DebugUtil.e(TAG, "isSmoothRadiusOn error: ${it.message}")
        }
        return false
    }
}