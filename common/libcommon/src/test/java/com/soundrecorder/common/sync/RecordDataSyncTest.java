package com.soundrecorder.common.sync;

import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.common.constant.RecordModeConstant;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.shadows.ShadowBaseUtils;
import com.soundrecorder.common.shadows.ShadowFeatureOption;
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.common.sync.db.RecordDataSync;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

import java.util.ArrayList;
import java.util.List;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowBaseUtils.class, ShadowFeatureOption.class})
public class RecordDataSyncTest {
    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @Test
    public void should_returnTrue_when_syncDataMedia() throws Exception {
        MockedStatic<BaseUtil> mockedStatic = Mockito.mockStatic(BaseUtil.class);
        mockedStatic.when(() -> BaseUtil.getPhoneStorageDir((Context) ArgumentMatchers.any())).thenReturn(null);
        Whitebox.invokeMethod(RecordDataSync.getInstance(), "syncDataMedia", mContext, false, false, RecordModeConstant.RECORD_TYPE_STANDARD);
        List<Record> mediaData = new ArrayList<>();
        List<Record> recordData = new ArrayList<>();
        Whitebox.invokeMethod(RecordDataSync.getInstance(), "diffMediaRecord", mContext, RecordModeConstant.RECORD_TYPE_STANDARD, mediaData, recordData);
        Assert.assertTrue(mediaData.size() == 0);
        mockedStatic.close();
    }

    @Test
    public void should_returnNull_when_checkRenameMediaFileFromOtherApp() throws Exception {
        MockedStatic<BaseUtil> mockedStatic = Mockito.mockStatic(BaseUtil.class);
        mockedStatic.when(() -> BaseUtil.getPhoneStorageDir((Context) ArgumentMatchers.any())).thenReturn(null);
        boolean isflag = Whitebox.invokeMethod(RecordDataSync.getInstance(), "checkRenameMediaFileFromOtherApp", mContext, new Record());
        Assert.assertFalse(isflag);
        mockedStatic.close();
    }

    @Test
    public void should_correct_when_setStopDiff() {
        RecordDataSync.getInstance().setStopDiff(true);
        boolean stop = Whitebox.getInternalState(RecordDataSync.getInstance(), "mStopDiff");
        Assert.assertTrue(stop);
        RecordDataSync.getInstance().setStopDiff(false);
        Assert.assertFalse(Whitebox.getInternalState(RecordDataSync.getInstance(), "mStopDiff"));
    }

    @After
    public void tearDown() {
        mContext = null;
    }
}
