package com.soundrecorder.common.buryingpoint;

import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.shadows.ShadowBaseUtils;
import com.soundrecorder.common.shadows.ShadowFeatureOption;
import com.soundrecorder.common.utils.RecordModeUtil;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.internal.verification.Times;
import org.robolectric.annotation.Config;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowBaseUtils.class, ShadowFeatureOption.class})
public class BuryingPointTest {

    private static final String TEST_STRING = "test_string";
    private static final boolean TEST_BOOLEAN = true;
    private static final int TEST_INT = 1;
    private static final long TEST_LONG = 1L;
    private Context mContext;
    private final MockedStatic<RecordModeUtil> mMockedStatic = Mockito.mockStatic(RecordModeUtil.class);
    private final MockedStatic<RecorderUserAction> mMock = Mockito.mockStatic(RecorderUserAction.class);

    @Before
    public void setUp() throws Exception {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @After
    public void tearDown() throws Exception {
        mContext = null;
        mMockedStatic.close();
        mMock.close();
    }

    @Test
    public void check_addRecordMode() {
        mMockedStatic.when(() -> RecordModeUtil.isSupportMultiRecordMode(mContext)).thenReturn(true);
        BuryingPoint.addRecordMode(TEST_STRING);
        mMockedStatic.when(() -> RecordModeUtil.isSupportMultiRecordMode(mContext)).thenReturn(false);
        BuryingPoint.addRecordMode(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()), Mockito.times(2));
    }

    @Test
    public void check_addRecordMode_name() {
        mMockedStatic.when(() -> RecordModeUtil.isSupportMultiRecordMode(mContext)).thenReturn(true);
        BuryingPoint.addRecordMode(TEST_STRING, TEST_STRING);
        mMockedStatic.when(() -> RecordModeUtil.isSupportMultiRecordMode(mContext)).thenReturn(false);
        BuryingPoint.addRecordMode(TEST_STRING, TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()), Mockito.times(2));
    }

    @Test
    public void check_addRecordType() {
        mMockedStatic.when(() -> RecordModeUtil.isSupportMultiRecordMode(any())).thenReturn(true);
        BuryingPoint.addRecordType(TEST_INT);
        mMockedStatic.when(() -> RecordModeUtil.isSupportMultiRecordMode(any())).thenReturn(false);
        BuryingPoint.addRecordType(TEST_INT);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()), Mockito.times(1));
    }

    @Test
    public void check_addRecordDuration() {
        BuryingPoint.addRecordDuration(TEST_LONG);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addRecordDurationMessage() {
        BuryingPoint.addRecordDurationMessage(TEST_LONG);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addRecordDurationNew() {
        BuryingPoint.addRecordDurationNew(TEST_LONG, TEST_INT, TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addRecordEntryLaunch() {
        BuryingPoint.addRecordEntryLaunch(TEST_STRING, TEST_INT);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addRecordSettingIconClick() {
        BuryingPoint.addRecordSettingIconClick(TEST_STRING, TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addRecordSearchDurRecords() {
        BuryingPoint.addRecordSearchDurRecords(TEST_STRING, TEST_INT, TEST_INT);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addRecordSpeakerEdit() {
        BuryingPoint.addRecordSpeakerEdit(TEST_STRING, TEST_STRING, TEST_STRING, TEST_LONG);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addRecordGroupState() {
        BuryingPoint.addRecordGroupState(TEST_STRING, TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addRecordDelete() {
        mMockedStatic.when(() -> RecordModeUtil.isSupportMultiRecordMode(mContext)).thenReturn(true);
        BuryingPoint.addRecordDelete(TEST_STRING);
        mMockedStatic.when(() -> RecordModeUtil.isSupportMultiRecordMode(mContext)).thenReturn(false);
        BuryingPoint.addRecordDelete(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()), Mockito.times(2));
    }

    @Test
    public void check_addRecordPlayDevice() {
        BuryingPoint.addRecordPlayDevice(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addRecordStartType() {
        BuryingPoint.addRecordStartType(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addSyncSwitch() {
        BuryingPoint.addSyncSwitch(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addRecordPlayState() {
        BuryingPoint.addRecordPlayState(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addMarkDelete() {
        mMockedStatic.when(() -> RecordModeUtil.isSupportMultiRecordMode(mContext)).thenReturn(true);
        BuryingPoint.addMarkDelete(TEST_INT);
        mMockedStatic.when(() -> RecordModeUtil.isSupportMultiRecordMode(mContext)).thenReturn(false);
        BuryingPoint.addMarkDelete(TEST_INT);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()), new Times(2));
    }

    @Test
    public void check_addMarkRename() {
        mMockedStatic.when(() -> RecordModeUtil.isSupportMultiRecordMode(mContext)).thenReturn(true);
        BuryingPoint.addMarkRename(TEST_INT);
        mMockedStatic.when(() -> RecordModeUtil.isSupportMultiRecordMode(mContext)).thenReturn(false);
        BuryingPoint.addMarkRename(TEST_INT);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()), new Times(2));
    }

    @Test
    public void check_addMarkAdd() {
        mMockedStatic.when(() -> RecordModeUtil.isSupportMultiRecordMode(mContext)).thenReturn(true);
        BuryingPoint.addMarkAdd(TEST_INT);
        mMockedStatic.when(() -> RecordModeUtil.isSupportMultiRecordMode(mContext)).thenReturn(false);
        BuryingPoint.addMarkAdd(TEST_INT);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()), new Times(2));
    }

    @Test
    public void check_addMarkWhenUnplay() {
        BuryingPoint.addMarkWhenUnplay();
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addMarkBtnClickFromNotification() {
        BuryingPoint.addMarkBtnClickFromNotification(TEST_BOOLEAN, TEST_INT);
        BuryingPoint.addMarkBtnClickFromNotification(!TEST_BOOLEAN, TEST_INT);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), any(), anyBoolean()), new Times(2));
    }

    @Test
    public void check_addPlayBtnClickFromNotification() {
        BuryingPoint.addPlayBtnClickFromNotification(TEST_BOOLEAN, TEST_BOOLEAN, TEST_INT);
        BuryingPoint.addPlayBtnClickFromNotification(TEST_BOOLEAN, !TEST_BOOLEAN, TEST_INT);
        BuryingPoint.addPlayBtnClickFromNotification(!TEST_BOOLEAN, TEST_BOOLEAN, TEST_INT);
        BuryingPoint.addPlayBtnClickFromNotification(!TEST_BOOLEAN, !TEST_BOOLEAN, TEST_INT);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), any(), anyBoolean()), new Times(4));
    }

    @Test
    public void check_addPlayFromAndDuration() {
        BuryingPoint.addPlayFromAndDuration(TEST_STRING, TEST_STRING, TEST_LONG);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_recordingAddPictureByCameraCancel() {
        BuryingPoint.recordingAddPictureByCameraCancel();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_playingAddPictureByCamera() {
        BuryingPoint.playingAddPictureByCamera();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_playingAddPictureByCameraCancel() {
        BuryingPoint.playingAddPictureByCameraCancel();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_playingAddPictureByCameraNumber() {
        BuryingPoint.playingAddPictureByCameraNumber();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_playingAddPictureByAlbum() {
        BuryingPoint.playingAddPictureByAlbum();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_playingAddPictureByAlbumCancel() {
        BuryingPoint.playingAddPictureByAlbumCancel();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_playingAddPictureByAlbumOk() {
        BuryingPoint.playingAddPictureByAlbumOk();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_playingAddPictureDialogNotOnClickNumber() {
        BuryingPoint.playingAddPictureDialogNotOnClickNumber();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_pictureMarkOnClickNumber() {
        BuryingPoint.pictureMarkOnClickNumber();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_selectAudioFormatClicked() {
        BuryingPoint.selectAudioFormatClicked(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addSkipMuteSwitch() {
        BuryingPoint.addSkipMuteSwitch(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyString(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_shouPopNumber() {
        BuryingPoint.shouPopNumber();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), any(), anyBoolean()));
    }

    @Test
    public void check_clickOnPopNumber() {
        BuryingPoint.clickOnPopNumber();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), any(), anyBoolean()));
    }

    @Test
    public void check_dismissPopNumberExcludeUserAction() {
        BuryingPoint.dismissPopNumberExcludeUserAction();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), any(), anyBoolean()));
    }

    @Test
    public void check_cancelAddPictureMarkNumber() {
        BuryingPoint.cancelAddPictureMarkNumber();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), any(), anyBoolean()));
    }

    @Test
    public void check_oKAddPictureMarkNumber() {
        BuryingPoint.oKAddPictureMarkNumber();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), any(), anyBoolean()));
    }

    @Test
    public void check_showPhotoViewerNumber() {
        BuryingPoint.showPhotoViewerNumber();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), any(), anyBoolean()));
    }

    @Test
    public void check_seekToMarkTagWhenPlayback() {
        mMockedStatic.when(() -> RecordModeUtil.isSupportMultiRecordMode(mContext)).thenReturn(true);
        BuryingPoint.seekToMarkTagWhenPlayback(TEST_INT);
        mMockedStatic.when(() -> RecordModeUtil.isSupportMultiRecordMode(mContext)).thenReturn(false);
        BuryingPoint.seekToMarkTagWhenPlayback(TEST_INT);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()), new Times(2));
    }

    @Test
    public void check_doClickOkOnUserNoticeDialog() {
        BuryingPoint.doClickOkOnUserNoticeDialog();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_doClickOkOnUserNoticeBasicDialog() {
        BuryingPoint.doClickOkOnUserNoticeBasicDialog();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_doClickOkOnUserNoticeBasicStillDialog() {
        BuryingPoint.doClickOkOnUserNoticeBasicStillDialog();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_doClickOkOnConvertPermissionDialog() {
        BuryingPoint.doClickOkOnConvertPermissionDialog();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_doClickOkOnConvertPermissionWithdrawnDialog() {
        BuryingPoint.doClickOkOnConvertPermissionWithdrawnDialog();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addActionForPlaybackRename() {
        BuryingPoint.addActionForPlaybackRename();
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addActionForPlaybackRenameSuccess() {
        BuryingPoint.addActionForPlaybackRenameSuccess();
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addActionForRenameBtnClick() {
        BuryingPoint.addActionForRenameBtnClick(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addClickMoreEdit() {
        BuryingPoint.addClickMoreEdit();
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addClickMoreSetting() {
        BuryingPoint.addClickMoreSetting();
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addClickRecordGroupPanel() {
        BuryingPoint.addClickRecordGroupPanel(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addClickBrowseRecordFile() {
        BuryingPoint.addClickBrowseRecordFile(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addClickRecordState() {
        BuryingPoint.addClickRecordState(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addClickSaveRecord() {
        BuryingPoint.addClickSaveRecord(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addClickCancelRecord() {
        BuryingPoint.addClickCancelRecord(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addClickPlayTextMark() {
        BuryingPoint.addClickPlayTextMark();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addClickSmallCardToRecord() {
        BuryingPoint.addClickSmallCardToRecord();
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addClickRecordTextMark() {
        BuryingPoint.addClickRecordTextMark();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addClickMoreShare() {
        BuryingPoint.addClickMoreShare();
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addSelectMoreRecordToShare() {
        BuryingPoint.addSelectMoreRecordToShare(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addClickRecordTextMarkInSmallCard() {
        BuryingPoint.addClickRecordTextMarkInSmallCard();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addClickBtnForLook() {
        BuryingPoint.addClickBtnForLook();
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addClickRecordTextMarkInMiniCard() {
        BuryingPoint.addClickRecordTextMarkInMiniCard();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addClickRecordStateInMiniCard() {
        BuryingPoint.addClickRecordStateInMiniCard(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addClickRecordStateInDragonFlyCard() {
        BuryingPoint.addClickRecordStateInDragonFlyCard(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addClickRecordStateInSmallCard() {
        BuryingPoint.addClickRecordStateInSmallCard(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addEnterSettingOpen() {
        BuryingPoint.addEnterSettingOpen();
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addFastBack() {
        BuryingPoint.addFastBack();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addFastForward() {
        BuryingPoint.addFastForward();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addPlayMarkListButton() {
        BuryingPoint.addPlayMarkListButton(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addPlaySetting() {
        BuryingPoint.addPlaySetting();
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyString(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addRestoreAll() {
        BuryingPoint.addRestoreAll();
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyString(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addPlayMoreDeleteSuccess() {
        BuryingPoint.addPlayMoreDeleteSuccess();
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addClickContentSearch() {
        BuryingPoint.addClickContentSearch();
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addActionMiniCancelRecord() {
        BuryingPoint.addActionMiniCancelRecord(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    public void check_addClickCancelContentSearch() {
        BuryingPoint.addClickCancelContentSearch();
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addContentSearchPosChange() {
        BuryingPoint.addContentSearchPosChange(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addThroughCallRecording() {
        BuryingPoint.addThroughCallRecording();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addLaunchAppSmallCard() {
        BuryingPoint.addLaunchAppSmallCard(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addFromBreeno() {
        BuryingPoint.addFromBreeno();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_seekToMarkTagWhenCutting() {
        mMockedStatic.when(() -> RecordModeUtil.isSupportMultiRecordMode(mContext)).thenReturn(true);
        BuryingPoint.seekToMarkTagWhenCutting(TEST_INT);
        mMockedStatic.when(() -> RecordModeUtil.isSupportMultiRecordMode(mContext)).thenReturn(false);
        BuryingPoint.seekToMarkTagWhenCutting(TEST_INT);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyInt(), anyString(), anyMap(), anyBoolean()), Mockito.times(2));
    }

    @Test
    public void check_addClickSetRingtone() {
        BuryingPoint.addClickSetRingtone(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addRecordPlayDeviceBrowse() {
        BuryingPoint.addRecordPlayDeviceBrowse(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addSearch() {
        BuryingPoint.addSearch(TEST_STRING);
        mMock.verify(() -> RecorderUserAction.addNewCommonUserAction(any(), anyString(), anyString(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_recordingAddPictureByCameraOk() {
        BuryingPoint.recordingAddPictureByCameraOk();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_recordingAddPictureByCamera() {
        BuryingPoint.recordingAddPictureByCamera();
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }

    @Test
    public void check_addPlaySwitchTab() {
        BuryingPoint.addPlaySwitchTab(0);
        mMock.verify(() -> RecorderUserAction.addCommonUserAction(any(), anyInt(), anyInt(), anyMap(), anyBoolean()));
    }
}
