/*********************************************************************
 * * Copyright (C), 2024, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummarySupportManager
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/5/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.util

import android.util.Log
import androidx.lifecycle.MutableLiveData
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.common.utils.CoroutineUtils
import com.soundrecorder.translate.AIAsrManager
import java.util.Locale

object SummarySupportManager {
    private const val TAG = "SummarySupportManager"
    @Volatile
    var supportRecordSummary = MutableLiveData<Boolean>()
        private set

    private var country: String? = Locale.getDefault().country

    @JvmStatic
    fun loadSupportSummaryByCountry(newCountry: String?) {
        if (newCountry != country) {
            Log.e(TAG, "loadSupportSummaryByCountry: country Changed, last = $country, new = $newCountry")
            country = newCountry

            CoroutineUtils.doInIOThread({
                loadSupportRecordSummary(true)
                /*切换国家，更新 ast能力 支持*/
                AIAsrManager.loadSupportAIAsrByCountry(newCountry)
            }, CoroutineUtils.mainScope)
        }
    }

    /**
     * 录音摘要的能力支持状态和之前不一致时发送live
     */
    @JvmStatic
    fun loadSupportRecordSummary(forceLoad: Boolean = false) {
        if (supportRecordSummary.value == null || forceLoad) {
            val newSupport = FeatureOption.supportRecordSummaryFunction()
                    && SummaryConditionChecker.isSupportRecordSummary(BaseApplication.getAppContext())
            Log.e(TAG, "loadSupportRecordSummary, oldData = ${supportRecordSummary.value}, newData = $newSupport")
            if (supportRecordSummary.value != newSupport) {
                supportRecordSummary.postValue(newSupport)
            }
        }
    }
}