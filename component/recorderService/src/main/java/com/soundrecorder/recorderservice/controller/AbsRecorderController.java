/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AbsRecorderController.java
 Description:
 Version: 1.0
 Date: 2020/1/7
 Author: liuyulong
 -----------Revision History-----------
 <author> <date> <version> <desc>
 liuyulong 2020/1/7 1.0 create
 */

package com.soundrecorder.recorderservice.controller;

import androidx.annotation.NonNull;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.recorderservice.RecordResult;
import com.soundrecorder.recorderservice.controller.observer.ControllerObserver;
import com.soundrecorder.recorderservice.controller.observer.RecordInfoSaveObserver;
import com.soundrecorder.recorderservice.controller.observer.WaveObserver;

public abstract class AbsRecorderController<T> {

    private static final String TAG = "AbsRecorderController";

    private AbsRecorderController<T> mInstance;

    private ControllerObserver<T> mControllerObserver;
    private WaveObserver mWaveObserver;
    private RecordInfoSaveObserver mRecordInfoSaveObserver;
    /*if service is start up by floating window, controllerScenes equals FLOATING,
     * else if form normal activity, controllerScenes equals NORMAL*/

    public ControllerObserver<T> getControllerObserver() {
        return mControllerObserver;
    }

    public WaveObserver getWaveObserver() {
        return mWaveObserver;
    }

    public void setWaveObserver(WaveObserver waveObserver) {
        this.mWaveObserver = waveObserver;
    }

    public RecordInfoSaveObserver getRecordInfoSaveObserver() {
        return mRecordInfoSaveObserver;
    }

    public void setRecordInfoSaveObserver(RecordInfoSaveObserver recordInfoSaveObserver) {
        this.mRecordInfoSaveObserver = recordInfoSaveObserver;
    }

    void registerControllerObserver(@NonNull ControllerObserver<T> controllerObserver) {
        DebugUtil.d(TAG, "registerRecorderControllerObserver");
        this.mControllerObserver = controllerObserver;
    }

    private void unRegisterControllerObserver() {
        DebugUtil.d(TAG, "unRegisterControllerObserver");
        if (mControllerObserver != null) {
            mControllerObserver = null;
        }
    }

    void registerWaveObserver(WaveObserver waveObserver) {
        DebugUtil.d(TAG, "registerWaveObserver");
        this.mWaveObserver = waveObserver;
    }

    private void unRegisterWaveObserver() {
        DebugUtil.d(TAG, "unRegisterWaveObserver");
        if (mWaveObserver != null) {
            mWaveObserver = null;
        }
    }

    void registerRecordInfoSaveObserver(RecordInfoSaveObserver recordInfoSaveObserver) {
        DebugUtil.d(TAG, "registerRecordInfoSaveObserver");
        this.mRecordInfoSaveObserver = recordInfoSaveObserver;
    }

    private void unRegisterRecordInfoSaveObserver() {
        DebugUtil.d(TAG, "unRegisterRecordInfoSaveObserver");
        if (mRecordInfoSaveObserver != null) {
            mRecordInfoSaveObserver = null;
        }
    }

    /**
     *初始化
     * @return AbsRecorderController
     */
    protected abstract AbsRecorderController initSample();

    /**
     *开始波形采样
     * @param delay 延时
     * @param interval 时间
     */
    protected abstract void startSample(int delay, int interval);

    /**
     *停止波形采样
     */
    protected abstract void stopSample();

    /**
     * 保存
     * @param displayName 传入录音名字
     * @param saveRecordFromWhere 保存来源
     * @param trigSyncNow 是否保存成功后立即触发云同步
     * @return 返回结果RecordResult
     */
    protected abstract RecordResult saveRecordInfo(String displayName, int saveRecordFromWhere, boolean trigSyncNow);

    protected void release() {
        DebugUtil.d(TAG, "unRegister all observer in controller");
        unRegisterControllerObserver();
        unRegisterWaveObserver();
        unRegisterRecordInfoSaveObserver();

        if (mInstance != null) {
            mInstance = null;
        }
    }
}
