/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: MarkMenuPopWindow
 Description:
 Version: 1.0
 Date: 2022/3/1
 Author: W9013333(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/3/1 1.0 create
 */

package com.soundrecorder.wavemark.mark.pop

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.text.TextUtils
import android.view.View
import androidx.core.content.ContextCompat
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.support.poplist.R
import java.util.Locale

@SuppressLint("PrivateResource")
@Suppress("TooGenericExceptionCaught")
class MarkMenuPopWindow(context: Context) : COUIPopupListWindow(context) {

    private val backgroundPaddingRect = Rect()

    init {
        try {
            ContextCompat.getDrawable(context, R.drawable.coui_popup_window_bg)?.getPadding(backgroundPaddingRect)
        } catch (_: Exception) {
        }
    }

    /**
     * 播放页面分屏时，计算menu菜单高度适配，计算的高度偏小
     */
    override fun measurePopupWindow(isAbove: Boolean) {
        super.measurePopupWindow(false)
    }

    override fun showAtLocation(parent: View?, gravity: Int, x: Int, y: Int) {
        if (parent == null) return
        val rect = Rect()
        parent.getGlobalVisibleRect(rect)
        val offsetX = if (isRTL()) {
            rect.centerX() - backgroundPaddingRect.left
        } else {
            rect.centerX() - width + backgroundPaddingRect.left
        }
        val offsetY = rect.centerY() - backgroundPaddingRect.top
        super.showAtLocation(parent, gravity, offsetX, offsetY)
    }

    /**
     * 在向上的基础上实现向下显示pop
     */
    fun showAtBelow(anchor: View) {
        show(anchor, false)
    }

    private fun isRTL(): Boolean {
        return TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == View.LAYOUT_DIRECTION_RTL
    }
}

fun showListPop(anchor: View, itemList: IntArray, onItemClickListener: (Int) -> Unit): MarkMenuPopWindow {
    return MarkMenuPopWindow(anchor.context).apply {
        setItemList(itemList.map { PopupListItem(anchor.context.getString(it), true) })
        setOnItemClickListener { _, _, position, _ ->
            if (position in itemList.indices) {
                onItemClickListener.invoke(itemList[position])
            }
        }
        setDismissWhenWindowSizeChange(false)
        setDismissTouchOutside(true)
        showAtBelow(anchor)
    }
}