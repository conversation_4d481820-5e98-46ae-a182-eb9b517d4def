<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.soundrecorder.setting">

    <application>
        <activity
            android:name=".setting.SettingRecorderActivity"
            android:configChanges="locale|keyboardHidden|screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:label="@string/app_name_main"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment" />

        <activity
            android:name=".about.RecordAboutActivity"
            android:configChanges="locale|orientation|keyboardHidden"
            android:exported="false"
            android:label="@string/app_name_main"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment" />
        <activity
            android:name=".opensource.OpenSourceActivity"
            android:configChanges="locale|keyboardHidden|screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:label="@string/app_name_main"
            android:screenOrientation="behind"
            android:theme="@style/AppBaseTheme.NoActionBar.ActionMode" />

        <activity
            android:name=".privacypolicy.PrivacyPolicyActivity"
            android:exported="false"
            android:label="@string/app_name_main"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment" />

        <activity
            android:name=".modelinfo.LargeModelInfoActivity"
            android:exported="false"
            android:label="@string/app_name_main"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment" />
    </application>

</manifest>