<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    android:splitMotionEvents="false">

    <com.google.android.material.appbar.COUIDividerAppBarLayout
        android:id="@+id/appbar"
        style="@style/CommonAppBarStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:focusable="true"
        android:orientation="vertical"
        app:elevation="0dp"
        app:layout_constraintTop_toTopOf="parent">

        <com.coui.appcompat.toolbar.COUIToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="@dimen/dp25"
            app:supportTitleTextAppearance="@style/textAppearanceSecondTitle" />

        <View
            android:id="@+id/divider_line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/divider_background_height"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="@dimen/dp24"
            android:layout_marginRight="@dimen/dp24"
            android:alpha="0"
            android:background="?attr/couiColorDivider"
            android:forceDarkAllowed="false" />

    </com.google.android.material.appbar.COUIDividerAppBarLayout>

    <TextView
        android:id="@+id/model_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp24"
        android:layout_marginTop="@dimen/dp12"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:lineHeight="@dimen/dp24"
        android:text="@string/model_name"
        android:textColor="?attr/couiColorLabelSecondary"
        android:textSize="@dimen/sp14"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appbar"/>

    <TextView
        android:id="@+id/record_number"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp24"
        android:layout_marginTop="@dimen/dp8"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:lineHeight="@dimen/dp24"
        android:text="@string/record_number"
        android:textColor="?attr/couiColorLabelSecondary"
        android:textSize="@dimen/sp14"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/model_name"/>
</androidx.constraintlayout.widget.ConstraintLayout>